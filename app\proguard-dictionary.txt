#
# This obfuscation dictionary contains names that are not allowed as file names
# in Windows, not even with extensions like .class or .java. They can however
# be used without problems in jar archives, which just begs to apply them as
# obfuscated class names. Trying to unpack the obfuscated archives in Windows
# will probably generate some sparks.
#

aux
Aux
aUx
AUx
auX
AuX
aUX
AUX
con
Con
cOn
COn
coN
CoN
cON
CON
nul
Nul
nUl
NUl
nuL
NuL
nUL
NUL
prn
Prn
pRn
PRn
prN
PrN
pRN
PRN
com1
Com1
cOm1
COm1
coM1
CoM1
cOM1
COM1
com2
Com2
cOm2
COm2
coM2
CoM2
cOM2
COM2
com3
Com3
cOm3
COm3
coM3
CoM3
cOM3
COM3
com4
Com4
cOm4
COm4
coM4
CoM4
cOM4
COM4
com5
Com5
cOm5
COm5
coM5
CoM5
cOM5
COM5
com6
Com6
cOm6
COm6
coM6
CoM6
cOM6
COM6
com7
Com7
cOm7
COm7
coM7
cOM7
COM7
com8
Com8
cOm8
COm8
coM8
CoM8
cOM8
COM8
com9
Com9
cOm9
COm9
coM9
CoM9
cOM9
COM9
lpt1
Lpt1
lPt1
LPt1
lpT1
LpT1
lPT1
LPT1
lpt2
Lpt2
lPt2
LPt2
lpT2
LpT2
lPT2
LPT2
lpt3
Lpt3
lPt3
LPt3
lpT3
LpT3
lPT3
LPT3
lpt4
Lpt4
lPt4
LPt4
lpT4
LpT4
lPT4
LPT4
lpt5
Lpt5
lPt5
LPt5
lpT5
LpT5
lPT5
LPT5
lpt6
Lpt6
lPt6
LPt6
lpT6
LpT6
lPT6
LPT6
lpt7
Lpt7
lPt7
LPt7
lpT7
LpT7
lPT7
LPT7
lpt8
Lpt8
lPt8
LPt8
lpT8
LpT8
LPT8
lpt9
Lpt9
lPt9
LPt9
lpT9
LpT9
lPT9
LPT9
