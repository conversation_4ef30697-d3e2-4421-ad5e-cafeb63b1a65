name: Crowdin Translations

on:
  push:
    branches:
      - main
    paths:
      - app/src/main/res/values*/strings.xml
  schedule:
    - cron: '0 9 * * 0' # Every Sunday at 9
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest
    steps:
      - name: Project checkout
        uses: actions/checkout@v4

      - name: Synchronize
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: true
          auto_approve_imported: true
          download_translations: true
          source: app/src/main/res/values/strings.xml
          translation: app/src/main/res/values-%two_letters_code%/strings.xml
          localization_branch_name: l10n_crowdin_translations
          create_pull_request: true
          pull_request_title: 'New Crowdin Translations'
          pull_request_body: 'New Crowdin translations by [Crowdin GH Action](https://github.com/crowdin/github-action)'
          pull_request_base_branch_name: 'main'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
