<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="conditional_incidents">

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="30-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/weather_widget_info.xml"
            line="10"
            column="5"
            startOffset="497"
            endLine="10"
            endColumn="32"
            endOffset="524"/>
        <map>
            <entry
                name="message"
                string="Attribute `targetCellWidth` is only used in API level 31 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="30-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="30-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/weather_widget_info.xml"
            line="11"
            column="5"
            startOffset="529"
            endLine="11"
            endColumn="33"
            endOffset="557"/>
        <map>
            <entry
                name="message"
                string="Attribute `targetCellHeight` is only used in API level 31 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="30-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="616"
                endOffset="636"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="15"
            column="9"
            startOffset="616"
            endLine="15"
            endColumn="29"
            endOffset="636"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
