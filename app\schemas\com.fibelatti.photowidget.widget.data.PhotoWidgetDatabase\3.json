{"formatVersion": 1, "database": {"version": 3, "identityHash": "92602aa766b1445d5c6a8f67e81c5b64", "entities": [{"tableName": "photo_widget_order", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoIndex` INTEGER NOT NULL, `photoId` TEXT NOT NULL, PRIMARY KEY(`widgetId`, `photoIndex`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoIndex", "columnName": "photoIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoIndex"]}, "indices": [], "foreignKeys": []}, {"tableName": "pending_deletion_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, `deletionTimestamp` INTEGER NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deletionTimestamp", "columnName": "deletionTimestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}, {"tableName": "excluded_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '92602aa766b1445d5c6a8f67e81c5b64')"]}}