package io.despicable.chromeos.data.datastore

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import io.despicable.chromeos.domain.model.FontSize
import io.despicable.chromeos.domain.model.IconSize
import io.despicable.chromeos.domain.model.WidgetConfiguration
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore preferences for widget configuration
 */
@Singleton
class WidgetPreferences @Inject constructor(
    private val context: Context
) {

    companion object {
        private const val TAG = "WidgetPreferences"

        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
            name = "widget_preferences"
        )

        // Preference keys
        private fun cityNameKey(widgetId: Int) = stringPreferencesKey("city_name_$widgetId")
        private fun iconSizeKey(widgetId: Int) = stringPreferencesKey("icon_size_$widgetId")
        private fun fontSizeKey(widgetId: Int) = stringPreferencesKey("font_size_$widgetId")
        private fun showHumidityKey(widgetId: Int) = booleanPreferencesKey("show_humidity_$widgetId")
        private fun showWindSpeedKey(widgetId: Int) = booleanPreferencesKey("show_wind_speed_$widgetId")
        private fun lastUpdatedKey(widgetId: Int) = longPreferencesKey("last_updated_$widgetId")
    }

    /**
     * Get widget configuration for a specific widget ID
     */
    fun getWidgetConfig(widgetId: Int): Flow<WidgetConfiguration?> {
        Log.d(TAG, "getWidgetConfig() called for widgetId: $widgetId")
        return context.dataStore.data.map { preferences ->
            Log.d(TAG, "Reading preferences for widgetId: $widgetId")
            val cityName = preferences[cityNameKey(widgetId)]
            Log.d(TAG, "City name from preferences: $cityName")
            if (cityName != null) {
                val config = WidgetConfiguration(
                    widgetId = widgetId,
                    cityName = cityName,
                    iconSize = IconSize.valueOf(
                        preferences[iconSizeKey(widgetId)] ?: IconSize.MEDIUM.name
                    ),
                    fontSize = FontSize.valueOf(
                        preferences[fontSizeKey(widgetId)] ?: FontSize.MEDIUM.name
                    ),
                    showHumidity = preferences[showHumidityKey(widgetId)] ?: true,
                    showWindSpeed = preferences[showWindSpeedKey(widgetId)] ?: true,
                    lastUpdated = preferences[lastUpdatedKey(widgetId)] ?: System.currentTimeMillis()
                )
                Log.d(TAG, "Returning config: $config")
                config
            } else {
                Log.d(TAG, "No city name found, returning null")
                null
            }
        }
    }

    /**
     * Save widget configuration
     */
    suspend fun saveWidgetConfig(config: WidgetConfiguration) {
        Log.d(TAG, "saveWidgetConfig() called with: $config")
        context.dataStore.edit { preferences ->
            Log.d(TAG, "Saving preferences for widgetId: ${config.widgetId}")
            preferences[cityNameKey(config.widgetId)] = config.cityName
            preferences[iconSizeKey(config.widgetId)] = config.iconSize.name
            preferences[fontSizeKey(config.widgetId)] = config.fontSize.name
            preferences[showHumidityKey(config.widgetId)] = config.showHumidity
            preferences[showWindSpeedKey(config.widgetId)] = config.showWindSpeed
            preferences[lastUpdatedKey(config.widgetId)] = config.lastUpdated
            Log.d(TAG, "Preferences saved successfully for widgetId: ${config.widgetId}")
        }
    }

    /**
     * Delete widget configuration
     */
    suspend fun deleteWidgetConfig(widgetId: Int) {
        context.dataStore.edit { preferences ->
            preferences.remove(cityNameKey(widgetId))
            preferences.remove(iconSizeKey(widgetId))
            preferences.remove(fontSizeKey(widgetId))
            preferences.remove(showHumidityKey(widgetId))
            preferences.remove(showWindSpeedKey(widgetId))
            preferences.remove(lastUpdatedKey(widgetId))
        }
    }

    /**
     * Check if widget configuration exists
     */
    fun widgetExists(widgetId: Int): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences.contains(cityNameKey(widgetId))
        }
    }
}
