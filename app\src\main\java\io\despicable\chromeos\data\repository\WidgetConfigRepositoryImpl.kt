package io.despicable.chromeos.data.repository

import android.util.Log
import io.despicable.chromeos.data.datastore.WidgetPreferences
import io.despicable.chromeos.domain.model.WidgetConfiguration
import io.despicable.chromeos.domain.repository.WidgetConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of WidgetConfigRepository using DataStore preferences
 */
@Singleton
class WidgetConfigRepositoryImpl @Inject constructor(
    private val widgetPreferences: WidgetPreferences
) : WidgetConfigRepository {

    companion object {
        private const val TAG = "WidgetConfigRepositoryImpl"
    }

    override suspend fun getWidgetConfig(widgetId: Int): WidgetConfiguration? {
        Log.d(TAG, "getWidgetConfig() called with widgetId: $widgetId")
        val result = widgetPreferences.getWidgetConfig(widgetId).first()
        Log.d(TAG, "getWidgetConfig() returning: $result")
        return result
    }

    override fun getAllWidgetConfigs(): Flow<List<WidgetConfiguration>> {
        Log.d(TAG, "getAllWidgetConfigs() called")
        // For simplicity, returning empty flow
        // In a real implementation, you might store widget IDs separately
        return flowOf(emptyList())
    }

    override suspend fun saveWidgetConfig(config: WidgetConfiguration) {
        Log.d(TAG, "saveWidgetConfig() called with: $config")
        widgetPreferences.saveWidgetConfig(config)
        Log.d(TAG, "saveWidgetConfig() completed successfully")
    }

    override suspend fun deleteWidgetConfig(widgetId: Int) {
        Log.d(TAG, "deleteWidgetConfig() called with widgetId: $widgetId")
        widgetPreferences.deleteWidgetConfig(widgetId)
    }

    override suspend fun widgetExists(widgetId: Int): Boolean {
        Log.d(TAG, "widgetExists() called with widgetId: $widgetId")
        val result = widgetPreferences.widgetExists(widgetId).first()
        Log.d(TAG, "widgetExists() returning: $result")
        return result
    }
}
