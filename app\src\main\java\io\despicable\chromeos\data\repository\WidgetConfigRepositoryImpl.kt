package io.despicable.chromeos.data.repository

import android.util.Log
import io.despicable.chromeos.data.datastore.WidgetPreferences
import io.despicable.chromeos.domain.model.WidgetConfiguration
import io.despicable.chromeos.domain.repository.WidgetConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of WidgetConfigRepository using DataStore preferences
 */
@Singleton
class WidgetConfigRepositoryImpl @Inject constructor(
    private val widgetPreferences: WidgetPreferences
) : WidgetConfigRepository {

    companion object {
        private const val TAG = "WidgetConfigRepositoryImpl"
    }

    override suspend fun getWidgetConfig(widgetId: Int): WidgetConfiguration? {
        return widgetPreferences.getWidgetConfig(widgetId).first()
    }

    override fun getAllWidgetConfigs(): Flow<List<WidgetConfiguration>> {
        // For simplicity, returning empty flow
        // In a real implementation, you might store widget IDs separately
        return flowOf(emptyList())
    }

    override suspend fun saveWidgetConfig(config: WidgetConfiguration) {
        widgetPreferences.saveWidgetConfig(config)
    }

    override suspend fun deleteWidgetConfig(widgetId: Int) {
        widgetPreferences.deleteWidgetConfig(widgetId)
    }

    override suspend fun widgetExists(widgetId: Int): Boolean {
        return widgetPreferences.widgetExists(widgetId).first()
    }
}
