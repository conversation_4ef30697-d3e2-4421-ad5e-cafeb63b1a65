http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/widget_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/weather_widget_preview.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/weather_widget_loading.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*buildDir}/generated/aboutLibraries/debug/res/raw/aboutlibraries.json,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/weather_widget_info.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:purple_200,0,V400020039,2e00020063,;"#FFBB86FC";white,0,V400080150,**********,;"#FFFFFFFF";teal_700,0,V4000600f7,2c0006011f,;"#FF018786";black,0,V400070125,290007014a,;"#FF000000";purple_700,0,V400040099,2e000400c3,;"#FF3700B3";purple_500,0,V400030069,2e00030093,;"#FF6200EE";teal_200,0,V4000500c9,2c000500f1,;"#FF03DAC5";+drawable:ic_launcher_foreground,1,F;ic_launcher_background,2,F;widget_background,3,F;weather_widget_preview,4,F;+layout:weather_widget_loading,5,F;+mipmap:ic_launcher_round,6,F;ic_launcher_round,7,F;ic_launcher_round,8,F;ic_launcher_round,9,F;ic_launcher_round,10,F;ic_launcher_round,11,F;ic_launcher,12,F;ic_launcher,13,F;ic_launcher,14,F;ic_launcher,15,F;ic_launcher,16,F;ic_launcher,17,F;+raw:aboutlibraries,18,F;+string:weather_widget_description,19,V40004005f,67000400c2,;"Weather widget with customizable display options";save_configuration,19,V4000f02c3,48000f0307,;"Save Widget Configuration";preview,19,V40010030d,2b00100334,;"Preview";app_name,19,V400010011,2d0001003a,;"chromeOS";select_city,19,V400090172,33000901a1,;"Select City";loading_weather,19,V4000500c8,3c00050100,;"Loading weather…";additional_info,19,V4000c0209,42000c0247,;"Additional Information";show_wind_speed,19,V4000e0286,3b000e02bd,;"Show Wind Speed";show_humidity,19,V4000d024d,37000d0280,;"Show Humidity";font_size,19,V4000b01d8,2f000b0203,;"Font Size";icon_size,19,V4000a01a7,2f000a01d2,;"Icon Size";configure_widget,19,V40008012b,450008016c,;"Configure Weather Widget";+style:Theme.ChromeOS,20,V40003003b,550003008c,;Dandroid\:Theme.Material.Light.NoActionBar,;+xml:weather_widget_info,21,F;data_extraction_rules,22,F;backup_rules,23,F;