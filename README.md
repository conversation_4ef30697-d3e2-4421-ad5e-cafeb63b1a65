# Weather Widget App with Real-time Preview

A comprehensive Android weather widget application built with **Jetpack Glance** featuring a **real-time configuration screen with live preview functionality**. The app follows Clean Architecture principles and uses modern Android development practices.

## 🎯 Features

### Core Features
- **Jetpack Glance Weather Widget**: Displays weather icon, temperature, city name, humidity, and wind speed
- **Real-time Configuration Screen**: Live preview that updates instantly as users make changes
- **Customizable Widget Options**:
  - City selection from predefined list
  - Weather icon size (Small, Medium, Large, Extra Large)
  - Font size for text elements
  - Toggle humidity and wind speed display
- **Sample Weather Data**: Pre-populated with 15 cities for testing
- **Clean Architecture**: Proper separation of concerns with Domain, Data, and Presentation layers

### Technical Features
- **MVVM Architecture** with Jetpack Compose
- **Koin Dependency Injection**
- **Room Database** for weather data persistence
- **DataStore Preferences** for widget configuration
- **Kotlin Coroutines & Flow** for reactive programming
- **WorkManager** for background weather updates
- **Material3 Design System**

## 🏗️ Architecture

### Clean Architecture Layers

```
┌─────────────────────────────────────┐
│         Presentation Layer          │
│  ┌─────────────────────────────────┐ │
│  │     Jetpack Compose UI          │ │
│  │     ViewModels (MVVM)           │ │
│  │     Widget (Jetpack Glance)     │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│           Domain Layer              │
│  ┌─────────────────────────────────┐ │
│  │     Use Cases                   │ │
│  │     Domain Models               │ │
│  │     Repository Interfaces       │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│            Data Layer               │
│  ┌─────────────────────────────────┐ │
│  │     Repository Impl.            │ │
│  │     Room Database               │ │
│  │     DataStore Preferences       │ │
│  │     Sample Data Provider        │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### Tech Stack
- **UI**: Jetpack Compose + Material3
- **Widget**: Jetpack Glance
- **Language**: 100% Kotlin
- **Database**: Room (SQLite)
- **Preferences**: DataStore
- **DI**: Koin
- **Async**: Kotlin Coroutines, Flow
- **Background Work**: WorkManager
- **Architecture**: Clean Architecture, MVVM

## 📁 Project Structure

```
app/src/main/java/io/despicable/chromeos/
├── di/                          # Dependency Injection
│   ├── DatabaseModule.kt
│   ├── RepositoryModule.kt
│   ├── UseCaseModule.kt
│   └── ViewModelModule.kt
├── data/                        # Data Layer
│   ├── database/
│   │   ├── WeatherDatabase.kt
│   │   ├── dao/WeatherDao.kt
│   │   └── entity/WeatherEntity.kt
│   ├── datastore/
│   │   └── WidgetPreferences.kt
│   └── repository/
│       ├── WeatherRepositoryImpl.kt
│       └── WidgetConfigRepositoryImpl.kt
├── domain/                      # Domain Layer
│   ├── model/
│   │   ├── WeatherData.kt
│   │   └── WidgetConfiguration.kt
│   ├── repository/
│   │   ├── WeatherRepository.kt
│   │   └── WidgetConfigRepository.kt
│   └── usecase/
│       ├── GetWeatherDataUseCase.kt
│       └── UpdateWidgetConfigUseCase.kt
├── presentation/                # Presentation Layer
│   ├── ui/
│   │   ├── config/
│   │   │   ├── WidgetConfigActivity.kt
│   │   │   ├── WidgetConfigScreen.kt
│   │   │   └── WidgetPreviewComponent.kt
│   │   └── widget/
│   │       ├── WeatherWidget.kt
│   │       └── WeatherWidgetProvider.kt
│   ├── viewmodel/
│   │   └── WidgetConfigViewModel.kt
│   └── worker/
│       └── WeatherUpdateWorker.kt
├── util/
│   └── SampleWeatherData.kt
├── WeatherApplication.kt
└── MainActivity.kt
```

## 🚀 Key Components

### 1. Real-time Widget Preview
The `WidgetPreviewComponent` provides a live preview that mirrors the actual widget appearance:
- Updates instantly when configuration changes
- Shows exact widget layout and styling
- Reflects all customization options in real-time

### 2. Configuration Screen
The `WidgetConfigScreen` offers comprehensive customization:
- **City Selection**: Horizontal scrollable chips for city selection
- **Icon Size**: Radio buttons for size selection (32dp - 80dp)
- **Font Size**: Radio buttons for text size (12sp - 24sp)
- **Additional Options**: Toggles for humidity and wind speed display

### 3. Jetpack Glance Widget
The `WeatherWidget` displays:
- Weather emoji icons based on conditions
- Temperature in Celsius
- City name
- Optional humidity percentage
- Optional wind speed in km/h

### 4. Data Management
- **Room Database**: Stores weather data for offline access
- **DataStore Preferences**: Persists widget configurations
- **Sample Data**: 15 pre-populated cities with random weather data

## 🔧 Setup & Installation

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 30+
- Kotlin 1.9+

### Installation Steps
1. Clone the repository
2. Open in Android Studio
3. Sync project with Gradle files
4. Run the app on device/emulator

### Adding Widgets
1. Long press on home screen
2. Select "Widgets"
3. Find "Weather Widget"
4. Drag to home screen
5. Configure in the setup screen

## 📱 Usage

### Main App
- Launch app to see demo configuration screen
- Tap "Configure Widget (Demo)" to test the configuration interface

### Widget Configuration
1. Select city from horizontal chip list
2. Choose icon size (Small to Extra Large)
3. Select font size for text elements
4. Toggle additional information display
5. Preview updates in real-time
6. Save configuration

### Widget Features
- Displays current weather information
- Updates automatically via WorkManager
- Customizable appearance based on configuration
- Supports multiple widget instances

## 🧪 Testing

The app includes sample weather data for 15 cities:
- New York, London, Tokyo, Paris, Sydney
- Berlin, Toronto, Mumbai, Singapore, Dubai
- Los Angeles, Chicago, Barcelona, Amsterdam, Seoul

Each city has randomized weather conditions for testing purposes.

## 🔮 Future Enhancements

### Planned Features
- **API Integration**: Real weather data from weather services
- **Location Services**: Auto-detect user location
- **Multiple Themes**: Dark mode and custom color schemes
- **Weather Alerts**: Notifications for severe weather
- **Extended Forecast**: 7-day weather forecast
- **Widget Sizes**: Support for different widget dimensions

### Compose Multiplatform Ready
The architecture is designed for easy migration to Compose Multiplatform:
- Platform-agnostic domain layer
- Abstracted dependencies behind interfaces
- Use of kotlinx libraries
- Minimal Android-specific code in business logic

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For questions or support, please open an issue in the GitHub repository.
