<?xml version="1.0" encoding="utf-8"?>
<appwidget-provider xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:configure="com.fibelatti.photowidget.configure.PhotoWidgetConfigureActivity"
    android:description="@string/photo_widget_host_description"
    android:minWidth="40dp"
    android:minHeight="40dp"
    android:previewImage="@drawable/widget_preview"
    android:previewLayout="@layout/photo_widget_preview"
    android:resizeMode="horizontal|vertical"
    android:targetCellWidth="2"
    android:targetCellHeight="2"
    android:updatePeriodMillis="0"
    android:widgetCategory="home_screen"
    android:widgetFeatures="reconfigurable"
    tools:targetApi="s" />
