<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/io/despicable/chromeos/presentation/ui/widget/WeatherWidget.kt"
            line="122"
            column="49"
            startOffset="4809"
            endLine="122"
            endColumn="93"
            endOffset="4853"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/io/despicable/chromeos/presentation/ui/config/WidgetPreviewComponent.kt"
            line="106"
            column="49"
            startOffset="4365"
            endLine="106"
            endColumn="93"
            endOffset="4409"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="19"
            column="13"
            startOffset="749"
            endLine="19"
            endColumn="45"
            endOffset="781"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.0 is available: 8.10.1">
        <fix-replace
            description="Change to 8.10.1"
            family="Update versions"
            robot="true"
            independent="true"
            oldString="8.10.0"
            replacement="8.10.1"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="18"
            endLine="2"
            endColumn="15"
            endOffset="26"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.1.20 is available: 2.1.21">
        <fix-replace
            description="Change to 2.1.21"
            family="Update versions"
            oldString="2.1.20"
            replacement="2.1.21"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="37"
            endLine="3"
            endColumn="18"
            endOffset="45"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.1.20 is available: 2.1.21">
        <fix-replace
            description="Change to 2.1.21"
            family="Update versions"
            oldString="2.1.20"
            replacement="2.1.21"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="37"
            endLine="3"
            endColumn="18"
            endOffset="45"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.serialization than 2.1.20 is available: 2.1.21">
        <fix-replace
            description="Change to 2.1.21"
            family="Update versions"
            oldString="2.1.20"
            replacement="2.1.21"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="37"
            endLine="3"
            endColumn="18"
            endOffset="45"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlinx:kotlinx-collections-immutable than 0.3.8 is available: 0.4.0">
        <fix-replace
            description="Change to 0.4.0"
            family="Update versions"
            oldString="0.3.8"
            replacement="0.4.0"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="11"
            column="33"
            startOffset="237"
            endLine="11"
            endColumn="40"
            endOffset="244"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.datastore:datastore-preferences-core than 1.1.6 is available: 1.1.7">
        <fix-replace
            description="Change to 1.1.7"
            family="Update versions"
            oldString="1.1.6"
            replacement="1.1.7"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="15"
            column="13"
            startOffset="300"
            endLine="15"
            endColumn="20"
            endOffset="307"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2025.05.00 is available: 2025.05.01">
        <fix-replace
            description="Change to 2025.05.01"
            family="Update versions"
            oldString="2025.05.00"
            replacement="2025.05.01"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="20"
            column="14"
            startOffset="377"
            endLine="20"
            endColumn="26"
            endOffset="389"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.compose.material3.adaptive:adaptive-navigation than 1.1.0-beta01 is available: 1.1.1">
        <fix-replace
            description="Change to 1.1.1"
            family="Update versions"
            oldString="1.1.0-beta01"
            replacement="1.1.1"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="24"
            column="20"
            startOffset="519"
            endLine="24"
            endColumn="34"
            endOffset="533"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.mikepenz.aboutlibraries.plugin than 12.0.1 is available: 12.1.2">
        <fix-replace
            description="Change to 12.1.2"
            family="Update versions"
            oldString="12.0.1"
            replacement="12.1.2"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="27"
            column="16"
            startOffset="563"
            endLine="27"
            endColumn="24"
            endOffset="571"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.mikepenz:aboutlibraries-compose than 12.0.1 is available: 12.1.2">
        <fix-replace
            description="Change to 12.1.2"
            family="Update versions"
            oldString="12.0.1"
            replacement="12.1.2"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="27"
            column="16"
            startOffset="563"
            endLine="27"
            endColumn="24"
            endOffset="571"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.mikepenz:aboutlibraries-core than 12.0.1 is available: 12.1.2">
        <fix-replace
            description="Change to 12.1.2"
            family="Update versions"
            oldString="12.0.1"
            replacement="12.1.2"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="27"
            column="16"
            startOffset="563"
            endLine="27"
            endColumn="24"
            endOffset="571"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.devtools.ksp than 2.1.20-2.0.0 is available: 2.1.21-2.0.1">
        <fix-replace
            description="Change to 2.1.21-2.0.1"
            family="Update versions"
            oldString="2.1.20-2.0.0"
            replacement="2.1.21-2.0.1"
            priority="0"/>
        <location
            file="$HOME/STUDIO/chromeOS_2/gradle/libs.versions.toml"
            line="38"
            column="7"
            startOffset="685"
            endLine="38"
            endColumn="21"
            endOffset="699"/>
    </incident>

</incidents>
