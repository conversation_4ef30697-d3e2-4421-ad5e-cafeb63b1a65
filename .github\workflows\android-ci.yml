name: Android CI

on: [ push, pull_request ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
      - name: Project checkout
        uses: actions/checkout@v4

      - name: Setup Android build
        uses: ./.github/actions/setup-android-build

      - name: Build
        run: ./gradlew assemble

      - name: Upload APK
        if: success()
        uses: actions/upload-artifact@v4
        with:
          path: app/build/outputs/apk/debug/*.apk

  code_analysis:
    name: Code Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Project checkout
        uses: actions/checkout@v4

      - name: Setup Android build
        uses: ./.github/actions/setup-android-build

      - name: Spotless check
        run: ./gradlew spotlessCheck

  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
      - name: Project checkout
        uses: actions/checkout@v4

      - name: Setup Android build
        uses: ./.github/actions/setup-android-build

      - name: Build
        run: ./gradlew lint

  license-check:
    name: License Check
    runs-on: ubuntu-latest
    steps:
      - name: Project checkout
        uses: actions/checkout@v4

      - name: Setup Android build
        uses: ./.github/actions/setup-android-build

      - name: Build
        run: ./gradlew licensee
