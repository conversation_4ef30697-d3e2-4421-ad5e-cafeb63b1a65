<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowBackground">@color/color_background</item>

        <item name="enableEdgeToEdge">true</item>

        <item name="materialAlertDialogTheme">@style/AppTheme.MaterialDialog</item>
    </style>

    <style name="AppTheme.Overlay" parent="ThemeOverlay.Material3.DynamicColors.DayNight">
        <item name="android:colorBackground">@color/color_background</item>
    </style>

    <style name="AppTheme.TransparentActivity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="AppTheme.MaterialDialog" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="android:windowBlurBehindEnabled" tools:targetApi="s">true</item>
        <item name="android:windowBlurBehindRadius" tools:targetApi="s">10dp</item>
    </style>

    <style name="AppTheme.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="android:windowBlurBehindEnabled" tools:targetApi="s">true</item>
        <item name="android:windowBlurBehindRadius" tools:targetApi="s">10dp</item>
    </style>
</resources>
