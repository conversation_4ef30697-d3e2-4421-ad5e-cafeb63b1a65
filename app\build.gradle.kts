plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.room)
    alias(libs.plugins.ksp)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.about.library)
}

android {
    namespace = "io.despicable.chromeos"
    compileSdk = 36

    defaultConfig {
        applicationId = "io.despicable.chromeos"
        minSdk = 30
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"
    }

    room {
        schemaDirectory("$projectDir/schemas")
        generateKotlin = true
    }

//    ksp {
//        arg("room.incremental", "true")
//        arg("room.generateKotlin", "true")
//    }


    signingConfigs {
        create("release") {
            storeFile = file("${project.rootDir}/keystore/keystore.jks")
            storePassword = "releaseO"
            keyAlias = "releaseO"
            keyPassword = "releaseO"
        }
        getByName("debug") {}
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            isCrunchPngs = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            isShrinkResources = false

            isDebuggable = true
            isDefault = true
            signingConfig = signingConfigs.getByName("debug")
            applicationIdSuffix = ".debug"
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
    }
    dependenciesInfo {
        // Disables dependency metadata when building APKs.
        includeInApk = false
        // Disables dependency metadata when building Android App Bundles.
        includeInBundle = false
    }
}

dependencies {

//    implementation(libs.appcompat)
//    implementation(libs.androidx.compose.foundation)
//    implementation(libs.androidx.compose.foundation.layout)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.compose.bom))
    implementation(libs.androidx.ui.graphics)
    androidTestImplementation(platform(libs.compose.bom))
    debugImplementation(libs.androidx.ui.tooling)
    implementation(libs.androidx.ui.tooling.preview)

    runtimeOnly(libs.kotlinx.coroutines.android)
    implementation(libs.bundles.compose)    // ui, material3, iconsExtended

    implementation(libs.room.ktx)
    implementation(libs.room.runtime)
    ksp(libs.room.compiler)

    implementation(libs.koin.compose)
//    implementation(libs.koin.core)
    implementation(libs.koin.android)

    // DataStore
    implementation(libs.androidx.datastore)
    implementation(libs.javax.inject)

    implementation(libs.kotlinx.datetime)

    implementation(libs.compose.navigation)
    implementation(libs.reorderable)

    // Work
    implementation(libs.androidx.workmanager)

    implementation(libs.kotlinx.serialization.json)

    implementation(libs.aboutlibrary.core)
    implementation(libs.aboutlibrary.compose)


    // Widgets
    implementation(libs.androidx.glance.material3)
    implementation(libs.androidx.glance)

    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)




}