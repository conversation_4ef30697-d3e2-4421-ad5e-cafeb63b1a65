package io.despicable.chromeos.data.repository

import android.util.Log
import io.despicable.chromeos.data.database.dao.WeatherDao
import io.despicable.chromeos.data.database.entity.toEntity
import io.despicable.chromeos.data.database.entity.toDomainModel
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.repository.WeatherRepository
import io.despicable.chromeos.util.SampleWeatherData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of WeatherRepository using Room database
 */
@Singleton
class WeatherRepositoryImpl @Inject constructor(
    private val weatherDao: WeatherDao
) : WeatherRepository {

    companion object {
        private const val TAG = "WeatherRepositoryImpl"
    }

    override suspend fun getWeatherByCity(cityName: String): WeatherData? {
        Log.d(TAG, "getWeatherByCity() called with: $cityName")
        val result = weatherDao.getWeatherByCity(cityName)?.toDomainModel()
        Log.d(TAG, "getWeatherByCity() returning: $result")
        return result
    }

    override fun getAllCities(): Flow<List<String>> {
        Log.d(TAG, "getAllCities() called")
        return weatherDao.getAllCities()
    }

    override fun getAllWeatherData(): Flow<List<WeatherData>> {
        Log.d(TAG, "getAllWeatherData() called")
        return weatherDao.getAllWeatherData().map { entities ->
            Log.d(TAG, "getAllWeatherData() mapping ${entities.size} entities")
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun insertWeatherData(weatherData: WeatherData) {
        Log.d(TAG, "insertWeatherData() called with: $weatherData")
        weatherDao.insertWeatherData(weatherData.toEntity())
    }

    override suspend fun deleteWeatherData(cityName: String) {
        Log.d(TAG, "deleteWeatherData() called with: $cityName")
        weatherDao.deleteWeatherByCity(cityName)
    }

    override suspend fun refreshWeatherData() {
        Log.d(TAG, "refreshWeatherData() called")
        // Check if database is empty and populate with sample data
        val count = weatherDao.getWeatherDataCount()
        Log.d(TAG, "Current weather data count: $count")
        if (count == 0) {
            Log.d(TAG, "Database is empty, inserting sample data")
            val sampleData = SampleWeatherData.getSampleWeatherData()
            Log.d(TAG, "Inserting ${sampleData.size} sample weather records")
            weatherDao.insertAllWeatherData(sampleData.map { it.toEntity() })
            Log.d(TAG, "Sample data inserted successfully")
        } else {
            Log.d(TAG, "Database already has data, skipping sample data insertion")
        }
        // In a real app, this would make API calls to refresh data
    }
}
