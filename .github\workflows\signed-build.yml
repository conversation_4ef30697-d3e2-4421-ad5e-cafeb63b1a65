name: Signed Build

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build-signed-apk:
    name: Build Signed APK
    runs-on: ubuntu-latest

    steps:
      - name: Project Checkout
        uses: actions/checkout@v4

      - name: Setup Android Build
        uses: ./.github/actions/setup-android-build

      - name: Decode Keystore
        run: |
          echo "${{ secrets.KEYSTORE }}" | base64 -d > ${{ github.workspace }}/keystore/release.jks

      - name: Build
        run: ./gradlew app:assembleRelease
        env:
          SIGN_BUILD: true
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}

      - name: Upload APK
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: signed-apk
          path: app/build/outputs/apk/release/*.apk

      - id: version
        name: Get version code
        if: ${{ github.ref_type == 'tag' }}
        run: |
          echo "VERSION_CODE=$(./gradlew -q app:printReleaseVersionCode | tail -n 1)" >> $GITHUB_OUTPUT

      - name: Create GitHub release
        if: ${{ github.ref_type == 'tag' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create ${{ github.ref_name }} \
            app/build/outputs/apk/release/*.apk \
            --title="${{ github.ref_name }}" \
            --notes-file fastlane/metadata/android/en-US/changelogs/${{ steps.version.outputs.VERSION_CODE }}.txt

  build-signed-aab:
    name: Build Signed AAB
    runs-on: ubuntu-latest

    steps:
      - name: Project Checkout
        uses: actions/checkout@v4

      - name: Setup Android Build
        uses: ./.github/actions/setup-android-build

      - name: Decode Keystore
        run: |
          echo "${{ secrets.KEYSTORE }}" | base64 -d > ${{ github.workspace }}/keystore/release.jks

      - name: Build
        run: ./gradlew app:bundleRelease
        env:
          SIGN_BUILD: true
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}

      - name: Upload AAB
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: signed-aab
          path: app/build/outputs/bundle/release/*.aab
