androidx.graphics.path.ConicConverter
androidx.core.graphics.drawable.IconCompat
androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver
io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider
androidx.compose.foundation.layout.UnspecifiedConstraintsElement
androidx.appcompat.widget.ContentFrameLayout
androidx.appcompat.view.menu.ExpandedMenuView
androidx.compose.ui.platform.AndroidComposeView$bringIntoViewNode$1
androidx.graphics.path.PathIteratorPreApi34Impl
androidx.appcompat.widget.AlertDialogLayout
androidx.appcompat.view.menu.ActionMenuItemView
androidx.glance.session.SessionWorker
androidx.compose.ui.input.key.KeyInputElement
androidx.appcompat.widget.ButtonBarLayout
androidx.work.impl.WorkDatabase_Impl
androidx.compose.foundation.text.modifiers.TextStringSimpleElement
io.despicable.chromeos.data.database.WeatherDatabase_Impl
androidx.work.impl.background.systemalarm.RescheduleReceiver
androidx.work.WorkerParameters
androidx.compose.foundation.selection.ToggleableElement
androidx.work.WorkManagerInitializer
androidx.core.app.RemoteActionCompatParcelizer
io.despicable.chromeos.data.database.WeatherDatabase
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.compose.ui.draganddrop.AndroidDragAndDropManager$modifier$1
androidx.compose.foundation.text.modifiers.TextAnnotatedStringElement
androidx.compose.ui.input.pointer.SuspendPointerInputElement
androidx.compose.foundation.selection.SelectableElement
androidx.compose.foundation.BackgroundElement
androidx.profileinstaller.ProfileInstallerInitializer
androidx.core.app.RemoteActionCompat
androidx.appcompat.widget.DialogTitle
androidx.emoji2.text.EmojiCompatInitializer
androidx.compose.material3.MinimumInteractiveModifier
androidx.compose.ui.semantics.EmptySemanticsElement
androidx.compose.foundation.lazy.layout.LazyLayoutItemAnimator$DisplayingDisappearingItemsElement
androidx.compose.foundation.BorderModifierNodeElement
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.work.CoroutineWorker
androidx.compose.foundation.layout.BoxChildDataElement
androidx.appcompat.widget.ActionBarContextView
androidx.appcompat.widget.ViewStubCompat
androidx.lifecycle.SavedStateHandlesVM
androidx.compose.foundation.layout.PaddingElement
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.compose.ui.input.rotary.RotaryInputElement
androidx.compose.ui.input.pointer.PointerHoverIconModifierElement
androidx.work.impl.background.systemalarm.SystemAlarmService
androidx.compose.foundation.text.modifiers.SelectableTextAnnotatedStringElement
androidx.work.impl.WorkDatabase
androidx.work.impl.workers.ConstraintTrackingWorker
androidx.work.impl.foreground.SystemForegroundService
androidx.startup.InitializationProvider
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.lifecycle.ProcessLifecycleOwner$attach$1
androidx.work.OverwritingInputMerger
io.despicable.chromeos.presentation.worker.WeatherUpdateWorker
androidx.compose.ui.draw.DrawBehindElement
androidx.compose.material3.ThumbElement
io.despicable.chromeos.MainActivity
androidx.work.impl.diagnostics.DiagnosticsReceiver
androidx.glance.appwidget.action.InvisibleActionTrampolineActivity
androidx.profileinstaller.ProfileInstallReceiver
androidx.compose.foundation.layout.WrapContentElement
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
androidx.appcompat.widget.Toolbar
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.compose.ui.layout.LayoutIdElement
androidx.work.impl.background.systemjob.SystemJobService
androidx.appcompat.widget.ActionMenuView
androidx.compose.ui.input.pointer.PointerInputEventHandler
androidx.room.MultiInstanceInvalidationService
androidx.core.app.CoreComponentFactory
androidx.appcompat.view.menu.ListMenuItemView
io.despicable.chromeos.WeatherApplication
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.glance.appwidget.MyPackageReplacedReceiver
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
androidx.lifecycle.ReportFragment
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.core.widget.RemoteViewsCompatService
androidx.compose.foundation.layout.PaddingValuesElement
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.glance.appwidget.action.ActionTrampolineActivity
androidx.appcompat.app.AlertController$RecycleListView
androidx.glance.appwidget.GlanceRemoteViewsService
androidx.compose.ui.layout.LayoutElement
android.support.v4.app.RemoteActionCompatParcelizer
androidx.compose.ui.semantics.AppendedSemanticsElement
androidx.compose.ui.focus.FocusOwnerImpl$modifier$1
androidx.work.impl.workers.DiagnosticsWorker
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
androidx.lifecycle.ProcessLifecycleInitializer
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.core.widget.NestedScrollView
androidx.compose.ui.graphics.GraphicsLayerElement
androidx.appcompat.widget.ActionBarContainer
androidx.compose.foundation.layout.FillElement
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.compose.foundation.layout.SizeElement
androidx.versionedparcelable.ParcelImpl
androidx.compose.foundation.lazy.layout.LazyLayoutSemanticsModifier
androidx.compose.foundation.lazy.layout.TraversablePrefetchStateModifierElement
androidx.lifecycle.ReportFragment$LifecycleCallbacks
io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
androidx.annotation.Keep
androidx.work.Worker
androidx.compose.foundation.IndicationModifierElement
androidx.glance.appwidget.UnmanagedSessionReceiver
androidx.versionedparcelable.CustomVersionedParcelable
androidx.compose.foundation.ClickableElement
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
androidx.compose.foundation.ScrollingContainerElement
androidx.compose.foundation.lazy.layout.LazyLayoutBeyondBoundsModifierElement
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int verticalAlignment_
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev$volatile
io.despicable.chromeos.domain.model.WeatherCondition: io.despicable.chromeos.domain.model.WeatherCondition$Companion Companion
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: int LAYOUT_FIELD_NUMBER
androidx.glance.appwidget.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int IDENTITY_FIELD_NUMBER
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int identity_
io.despicable.chromeos.domain.model.IconSize: io.despicable.chromeos.domain.model.IconSize$Companion Companion
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object tail$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: androidx.glance.appwidget.protobuf.Parser PARSER
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: int nextIndex_
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: androidx.glance.appwidget.protobuf.Parser PARSER
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur$volatile
androidx.glance.appwidget.protobuf.AbstractMessageLite: int memoizedHashCode
kotlinx.coroutines.DefaultExecutor: int debugStatus
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: int bitField0_
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler$volatile
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: androidx.glance.appwidget.proto.LayoutProto$LayoutNode layout_
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int HORIZONTAL_ALIGNMENT_FIELD_NUMBER
kotlinx.coroutines.internal.ThreadSafeHeap: int _size$volatile
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue$volatile
kotlinx.coroutines.channels.BufferedChannel: long receivers$volatile
io.despicable.chromeos.domain.model.FontSize: io.despicable.chromeos.domain.model.FontSize$Companion Companion
io.despicable.chromeos.domain.model.WeatherData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment$volatile
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment$volatile
kotlinx.coroutines.AwaitAll$AwaitAllNode: java.lang.Object _disposer$volatile
kotlinx.coroutines.InvokeOnCancelling: int _invoked$volatile
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
kotlin.coroutines.SafeContinuation: java.lang.Object result
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: androidx.glance.appwidget.proto.LayoutProto$LayoutNode DEFAULT_INSTANCE
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int HASACTION_FIELD_NUMBER
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next$volatile
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: androidx.glance.appwidget.proto.LayoutProto$LayoutConfig DEFAULT_INSTANCE
kotlin.SafePublicationLazyImpl: java.lang.Object _value
io.despicable.chromeos.domain.model.WidgetConfiguration$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int HAS_IMAGE_DESCRIPTION_FIELD_NUMBER
kotlinx.coroutines.JobSupport: java.lang.Object _state$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: boolean hasImageDescription_
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus$volatile
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state$volatile
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder$volatile
androidx.glance.appwidget.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
androidx.glance.appwidget.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int imageScale_
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed$volatile
kotlinx.coroutines.AwaitAll: int notCompletedCount$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int WIDTH_FIELD_NUMBER
androidx.glance.appwidget.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: androidx.glance.appwidget.protobuf.Internal$ProtobufList children_
kotlinx.coroutines.CancelledContinuation: int _resumed$volatile
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int TYPE_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.glance.appwidget.protobuf.GeneratedMessageLite: androidx.glance.appwidget.protobuf.UnknownFieldSetLite unknownFields
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long enqIdx$volatile
kotlinx.coroutines.DispatchedCoroutine: int _decision$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int width_
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int IMAGE_SCALE_FIELD_NUMBER
io.despicable.chromeos.domain.model.WeatherData: io.despicable.chromeos.domain.model.WeatherData$Companion Companion
kotlinx.coroutines.internal.Segment: int cleanedAndPointers$volatile
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: boolean hasAction_
kotlinx.coroutines.CompletedExceptionally: int _handled$volatile
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: int _availablePermits$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState$volatile
androidx.compose.foundation.lazy.layout.DefaultLazyKey: android.os.Parcelable$Creator CREATOR
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: int NEXT_INDEX_FIELD_NUMBER
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer$volatile
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef$volatile
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask$volatile
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
kotlinx.coroutines.EventLoopImplBase: int _isCompleted$volatile
androidx.compose.runtime.ParcelableSnapshotMutableFloatState: android.os.Parcelable$Creator CREATOR
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: int LAYOUT_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
androidx.lifecycle.Lifecycle$Event: kotlin.enums.EnumEntries $ENTRIES
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int CHILDREN_FIELD_NUMBER
androidx.glance.appwidget.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: int LAYOUT_INDEX_FIELD_NUMBER
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: boolean hasImageColorFilter_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long deqIdx$volatile
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: androidx.glance.appwidget.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex$volatile
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int type_
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev$volatile
androidx.compose.runtime.ParcelableSnapshotMutableState: android.os.Parcelable$Creator CREATOR
androidx.glance.appwidget.protobuf.GeneratedMessageLite: int memoizedSerializedSize
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl$volatile
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int HAS_IMAGE_COLOR_FILTER_FIELD_NUMBER
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int horizontalAlignment_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment$volatile
io.despicable.chromeos.domain.model.WidgetConfiguration: io.despicable.chromeos.domain.model.WidgetConfiguration$Companion Companion
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause$volatile
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object head$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int HEIGHT_FIELD_NUMBER
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int height_
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: int layoutIndex_
kotlinx.coroutines.EventLoopImplBase$DelayedTask: java.lang.Object _heap
androidx.glance.appwidget.proto.LayoutProto$LayoutNode: int VERTICAL_ALIGNMENT_FIELD_NUMBER
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next$volatile
androidx.compose.runtime.ParcelableSnapshotMutableIntState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd$volatile
androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition: androidx.glance.appwidget.proto.LayoutProto$LayoutDefinition DEFAULT_INSTANCE
androidx.glance.appwidget.proto.LayoutProto$LayoutConfig: androidx.glance.appwidget.protobuf.Internal$ProtobufList layout_
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack$volatile
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state$volatile
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle$volatile
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
androidx.glance.appwidget.proto.LayoutProto$ContentScale: androidx.glance.appwidget.proto.LayoutProto$ContentScale valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
io.despicable.chromeos.domain.model.WidgetConfiguration$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners get_viewTreeOwners()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority[] values()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboardManager getClipboardManager()
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType valueOf(java.lang.String)
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.layout.Placeable$PlacementScope getPlacementScope()
androidx.glance.appwidget.LayoutSize: androidx.glance.appwidget.LayoutSize[] values()
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight valueOf(java.lang.String)
androidx.glance.appwidget.protobuf.FieldType$Collection: androidx.glance.appwidget.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.activity.EdgeToEdgeApi29: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.compose.ui.platform.AndroidComposeView: void getFontLoader$annotations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorPeek(long)
androidx.glance.appwidget.CollectionItemsApi31Impl: void setRemoteAdapter(android.widget.RemoteViews,int,androidx.glance.appwidget.RemoteCollectionItems)
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens[] values()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
androidx.glance.appwidget.protobuf.ProtoSyntax: androidx.glance.appwidget.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.work.impl.workers.DiagnosticsWorker: DiagnosticsWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.despicable.chromeos.domain.model.IconSize: io.despicable.chromeos.domain.model.IconSize valueOf(java.lang.String)
io.despicable.chromeos.domain.model.FontSize: io.despicable.chromeos.domain.model.FontSize valueOf(java.lang.String)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.glance.appwidget.proto.LayoutProto$LayoutType: androidx.glance.appwidget.proto.LayoutProto$LayoutType valueOf(java.lang.String)
androidx.glance.appwidget.proto.LayoutProto$NodeIdentity: androidx.glance.appwidget.proto.LayoutProto$NodeIdentity[] values()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.compose.ui.platform.AndroidComposeView: kotlin.jvm.functions.Function1 getConfigurationChangeObserver()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void setLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.glance.appwidget.TracingApi29Impl: void beginAsyncSection(java.lang.String,int)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillManager getAutofillManager()
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.appcompat.widget.TooltipCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl34)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.glance.appwidget.proto.LayoutProto$LayoutType: androidx.glance.appwidget.proto.LayoutProto$LayoutType[] values()
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsO: void focusable(android.view.View,int,boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.concurrent.futures.DirectExecutor: androidx.concurrent.futures.DirectExecutor valueOf(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void setTransitionGroup(boolean)
io.despicable.chromeos.domain.model.WeatherCondition: io.despicable.chromeos.domain.model.WeatherCondition valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.glance.appwidget.CollectionItemsApi31Impl: android.widget.RemoteViews$RemoteCollectionItems toPlatformCollectionItems(androidx.glance.appwidget.RemoteCollectionItems)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.spatial.RectManager getRectManager()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl30: boolean isVisible(int)
androidx.activity.EdgeToEdgeApi23: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.glance.appwidget.protobuf.WireFormat$FieldType: androidx.glance.appwidget.protobuf.WireFormat$FieldType[] values()
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void set_viewTreeOwners(androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.compose.ui.platform.AndroidComposeView: void setConfigurationChangeObserver(kotlin.jvm.functions.Function1)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void setViewTranslationCallback(android.view.View)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy valueOf(java.lang.String)
io.despicable.chromeos.domain.model.FontSize: io.despicable.chromeos.domain.model.FontSize[] values()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver: ConstraintProxyUpdateReceiver()
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.work.Worker: Worker(android.content.Context,androidx.work.WorkerParameters)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.DropDownListView$Api33Impl: void setSelectedChildViewEnabled(android.widget.AbsListView,boolean)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isVisible(int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.compose.ui.platform.CalculateMatrixToWindowApi29: void calculateMatrixToWindow-EL8BTi8(android.view.View,float[])
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus valueOf(java.lang.String)
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillTree getAutofillTree()
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.compose.ui.platform.AndroidComposeView: boolean getScrollCaptureInProgress$ui_release()
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setBlendMode(android.widget.RemoteViews,int,java.lang.String,android.graphics.BlendMode)
androidx.compose.runtime.SlotTableKt: void throwConcurrentModificationException()
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.compose.runtime.collection.MutableVectorKt: void throwOutOfRangeException(int,int)
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.glance.appwidget.translators.TextTranslatorApi31Impl: void setTextViewGravity(android.widget.RemoteViews,int,int)
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.IntObjectMap getLayoutNodes()
androidx.glance.appwidget.LayoutType: androidx.glance.appwidget.LayoutType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.work.OverwritingInputMerger: OverwritingInputMerger()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setEpicenterBounds(android.widget.PopupWindow,android.graphics.Rect)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.compose.ui.platform.AbstractComposeView: boolean getHasComposition()
androidx.glance.appwidget.action.ActionTrampolineType: androidx.glance.appwidget.action.ActionTrampolineType[] values()
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setIsClippedToScreen(android.widget.PopupWindow,boolean)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.SoftwareKeyboardController getSoftwareKeyboardController()
androidx.appcompat.widget.AppCompatTextHelper$Api28Impl: android.graphics.Typeface create(android.graphics.Typeface,int,boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.core.widget.RemoteViewsCompatService: RemoteViewsCompatService()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorInt(android.widget.RemoteViews,int,java.lang.String,int,int)
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy: ConstraintProxy$BatteryChargingProxy()
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.compose.runtime.ComposerKt: java.lang.Void composeRuntimeError(java.lang.String)
androidx.work.WorkManagerInitializer: WorkManagerInitializer()
androidx.compose.ui.platform.AbstractComposeView: void setParentContext(androidx.compose.runtime.CompositionContext)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setFloatDimenAttr(android.widget.RemoteViews,int,java.lang.String,int)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewUtils$Api29Impl: void computeFitSystemWindows(android.view.View,android.graphics.Rect,android.graphics.Rect)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.core.view.WindowInsetsCompat$Impl20: void setSystemUiVisibility(int)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
org.koin.core.definition.Kind: org.koin.core.definition.Kind[] values()
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.input.TextInputService getTextInputService()
androidx.glance.appwidget.WidgetLayoutImpl31: androidx.glance.appwidget.proto.LayoutProto$DimensionType toProto(androidx.glance.unit.Dimension)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AndroidAutofillManager get_autofillManager$ui_release()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners getViewTreeOwners()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.compose.ui.platform.ViewLayer: float getCameraDistancePx()
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.compose.ui.graphics.layer.ViewLayer: void setInvalidated(boolean)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.compose.ui.node.LayoutNode: java.lang.String exceptionMessageForParentingOrOwnership(androidx.compose.ui.node.LayoutNode)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setIntDimen(android.widget.RemoteViews,int,java.lang.String,int)
androidx.compose.ui.platform.AndroidComposeView: void setAccessibilityEventBatchIntervalMillis(long)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.DragAndDropManager getDragAndDropManager()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.TextToolbar getTextToolbar()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.work.impl.background.systemalarm.RescheduleReceiver: RescheduleReceiver()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.glance.appwidget.ApplyModifiersApi31Impl: void setViewHeight(android.widget.RemoteViews,int,androidx.glance.unit.Dimension)
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority valueOf(java.lang.String)
androidx.graphics.path.PathIteratorPreApi34Impl: boolean internalPathIteratorHasNext(long)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.glance.appwidget.action.ListAdapterTrampolineApi26Impl: void startForegroundService(android.content.Context,android.content.Intent)
androidx.activity.EdgeToEdgeApi28: void adjustLayoutInDisplayCutoutMode(android.view.Window)
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy: ConstraintProxy$BatteryNotLowProxy()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.glance.session.SessionWorker: SessionWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.glance.appwidget.protobuf.WireFormat$JavaType: androidx.glance.appwidget.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.activity.ComponentActivity: void setContentView(android.view.View)
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setEnterTransition(android.widget.PopupWindow,android.transition.Transition)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy[] values()
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: void setTextLocales(android.widget.TextView,android.os.LocaleList)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.compose.runtime.collection.MutableVectorKt: void throwNegativeIndexException(int)
androidx.glance.appwidget.GeneratedContainersForApi31Impl: java.util.Map registerChildren()
androidx.appcompat.widget.AppCompatTextClassifierHelper$Api26Impl: android.view.textclassifier.TextClassifier getTextClassifier(android.widget.TextView)
org.koin.core.definition.Kind: org.koin.core.definition.Kind valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.MenuPopupWindow$Api29Impl: void setTouchModal(android.widget.PopupWindow,boolean)
androidx.glance.appwidget.proto.LayoutProto$DimensionType: androidx.glance.appwidget.proto.LayoutProto$DimensionType[] values()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.glance.appwidget.action.ApplyActionApi31Impl: void unsetOnClickResponse(android.widget.RemoteViews,int)
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy[] values()
androidx.compose.ui.graphics.layer.ViewLayer: android.view.View getOwnerView()
androidx.compose.ui.platform.AbstractComposeView: void getDisposeViewCompositionStrategy$annotations()
io.despicable.chromeos.domain.model.WeatherCondition: io.despicable.chromeos.domain.model.WeatherCondition[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.glance.appwidget.TracingApi29Impl: void endAsyncSection(java.lang.String,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.room.MultiInstanceInvalidationService: MultiInstanceInvalidationService()
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.compose.ui.platform.AbstractComposeView: void getShowLayoutBounds$annotations()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
androidx.emoji2.text.EmojiExclusions$EmojiExclusions_Api34: java.util.Set getExclusions()
androidx.work.DirectExecutor: androidx.work.DirectExecutor valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
io.despicable.chromeos.domain.model.FontSize$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction valueOf(java.lang.String)
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy valueOf(java.lang.String)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.compose.ui.platform.AndroidComposeView: int getImportantForAutofill()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.compose.ui.platform.AndroidComposeView: void setDensity(androidx.compose.ui.unit.Density)
androidx.compose.ui.platform.AndroidComposeView: void getLastMatrixRecalculationAnimationTime$ui_release$annotations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax[] values()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.compose.ui.platform.ViewLayer: long getOwnerViewId()
androidx.glance.appwidget.translators.ImageTranslatorApi23Impl: void setImageViewIcon(android.widget.RemoteViews,int,android.graphics.drawable.Icon)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.hapticfeedback.HapticFeedback getHapticFeedBack()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.compose.ui.platform.AndroidComposeView: long getLastMatrixRecalculationAnimationTime$ui_release()
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setIcon(android.widget.RemoteViews,int,java.lang.String,android.graphics.drawable.Icon,android.graphics.drawable.Icon)
androidx.glance.appwidget.action.ApplyActionApi31Impl: void unsetOnCheckedChangeResponse(android.widget.RemoteViews,int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.work.NetworkType: androidx.work.NetworkType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.AppCompatTextHelper$Api21Impl: java.util.Locale forLanguageTag(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.AppCompatTextView: void setAllCaps(boolean)
androidx.work.impl.background.systemalarm.SystemAlarmService: SystemAlarmService()
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
androidx.glance.appwidget.GlanceRemoteViewsServiceKt: void setRemoteAdapter(android.widget.RemoteViews,android.content.Context,int,int,java.lang.String,androidx.glance.appwidget.RemoteCollectionItems)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboard getClipboard()
androidx.concurrent.futures.DirectExecutor: androidx.concurrent.futures.DirectExecutor[] values()
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedCallback newOnBackInvokedCallback(java.lang.Runnable)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.compose.ui.platform.AndroidComposeViewForceDarkModeQ: void disallowForceDark(android.view.View)
androidx.appcompat.widget.Toolbar$Api33Impl: void tryUnregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.glance.appwidget.action.ApplyActionApi26Impl: android.app.PendingIntent getForegroundServicePendingIntent(android.content.Context,android.content.Intent)
androidx.glance.appwidget.protobuf.FieldType: androidx.glance.appwidget.protobuf.FieldType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AccessibilityManager getAccessibilityManager()
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.appcompat.widget.Toolbar: java.util.ArrayList getCurrentMenuItems()
androidx.core.view.WindowInsetsCompat$Impl: boolean isVisible(int)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.compose.material3.MinimumInteractiveModifier: MinimumInteractiveModifier()
androidx.glance.appwidget.GlanceRemoteViewsService: GlanceRemoteViewsService()
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation[] values()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorRawSize(long)
androidx.glance.appwidget.protobuf.FieldType: androidx.glance.appwidget.protobuf.FieldType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.compose.ui.platform.AndroidComposeView: void setOnViewTreeOwnersAvailable(kotlin.jvm.functions.Function1)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setFloatDimen(android.widget.RemoteViews,int,java.lang.String,int)
androidx.glance.appwidget.protobuf.FieldType$Collection: androidx.glance.appwidget.protobuf.FieldType$Collection[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.glance.appwidget.UnmanagedSessionReceiver: UnmanagedSessionReceiver()
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.glance.appwidget.protobuf.ProtoSyntax: androidx.glance.appwidget.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void setShowLayoutBounds(boolean)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.activity.EdgeToEdgeApi30: void adjustLayoutInDisplayCutoutMode(android.view.Window)
androidx.compose.runtime.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction[] values()
androidx.glance.appwidget.ApplyModifiersApi31Impl: void applyRoundedCorners(android.widget.RemoteViews,int,androidx.glance.unit.Dimension)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.RootForTest getRootForTest()
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.activity.EdgeToEdgeApi21: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.compose.ui.platform.AndroidComposeView: void getTextInputService$annotations()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.platform.AndroidComposeView: void setContentCaptureManager$ui_release(androidx.compose.ui.contentcapture.AndroidContentCaptureManager)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColor(android.widget.RemoteViews,int,java.lang.String,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent[] values()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight[] values()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.appcompat.widget.AppCompatTextView: void setFilters(android.text.InputFilter[])
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.AndroidComposeView getOwnerView()
androidx.startup.InitializationProvider: InitializationProvider()
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setInsets(int,androidx.core.graphics.Insets)
androidx.compose.ui.platform.AndroidComposeView: void setLastMatrixRecalculationAnimationTime$ui_release(long)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.ui.unit.ConstraintsKt: java.lang.Void throwInvalidConstraintsSizeException(int)
androidx.compose.material3.ColorResourceHelper: long getColor-WaAFU9c(android.content.Context,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
io.despicable.chromeos.data.database.WeatherDatabase: WeatherDatabase()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.compose.ui.graphics.layer.view.DrawChildContainer: int getChildCount()
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.glance.unit.ColorProviderApi23Impl: int getColor(android.content.Context,int)
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens[] values()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType[] values()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setIntDimen(android.widget.RemoteViews,int,java.lang.String,float,int)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass[] values()
androidx.graphics.path.PathIteratorPreApi34Impl: void destroyInternalPathIterator(long)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.compose.ui.platform.AbstractComposeView: void setViewCompositionStrategy(androidx.compose.ui.platform.ViewCompositionStrategy)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.compose.ui.platform.AbstractComposeView: void setPreviousAttachedWindowToken(android.os.IBinder)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.compose.ui.graphics.layer.ViewLayer: androidx.compose.ui.graphics.CanvasHolder getCanvasHolder()
androidx.glance.appwidget.protobuf.Writer$FieldOrder: androidx.glance.appwidget.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult valueOf(java.lang.String)
androidx.compose.ui.platform.ViewLayer: long getLayerId()
io.despicable.chromeos.data.database.WeatherDatabase_Impl: WeatherDatabase_Impl()
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithConfiguration(android.widget.TextView,int,int,int,int)
androidx.compose.ui.text.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
androidx.appcompat.widget.Toolbar: void setBackInvokedCallbackEnabled(boolean)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: boolean setFontVariationSettings(android.widget.TextView,java.lang.String)
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens valueOf(java.lang.String)
androidx.compose.animation.core.PreconditionsKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction[] values()
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.despicable.chromeos.presentation.worker.WeatherUpdateWorker: WeatherUpdateWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.work.impl.workers.ConstraintTrackingWorker: ConstraintTrackingWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.glance.appwidget.GeneratedContainersForApi31Impl: java.util.Map registerContainers()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.compose.ui.platform.ComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.glance.appwidget.action.ApplyActionApi31Impl: void setOnCheckedChangeResponse(android.widget.RemoteViews,int,android.content.Intent)
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation valueOf(java.lang.String)
io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider: WeatherWidgetProvider()
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.modifier.ModifierLocalManager getModifierLocalManager()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.LayoutDirection getLayoutDirection()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.core.view.WindowInsetsCompat$TypeImpl34: int toPlatformType(int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setFloatDimen(android.widget.RemoteViews,int,java.lang.String,float,int)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.glance.appwidget.action.ActionTrampolineType: androidx.glance.appwidget.action.ActionTrampolineType valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.compose.ui.platform.ViewLayer: void setInvalidated(boolean)
androidx.appcompat.widget.AppCompatTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.glance.appwidget.translators.CompoundButtonApi31Impl: void setCompoundButtonChecked(android.widget.RemoteViews,int,boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass valueOf(java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.core.widget.NestedScrollView: androidx.core.view.ScrollFeedbackProviderCompat getScrollFeedbackProvider()
androidx.glance.appwidget.translators.ImageTranslatorApi31Impl: void applyTintColorFilter(androidx.glance.appwidget.TranslationContext,android.widget.RemoteViews,androidx.glance.unit.ColorProvider,int)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidAccessibilityManager getAccessibilityManager()
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.glance.appwidget.MyPackageReplacedReceiver: MyPackageReplacedReceiver()
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection[] values()
androidx.glance.appwidget.protobuf.Writer$FieldOrder: androidx.glance.appwidget.protobuf.Writer$FieldOrder[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.work.impl.foreground.SystemForegroundService: SystemForegroundService()
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy: ConstraintProxy$NetworkStateProxy()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void clearViewTranslationCallback(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction valueOf(java.lang.String)
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState[] values()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isTypeVisible(int)
androidx.glance.appwidget.protobuf.WireFormat$JavaType: androidx.glance.appwidget.protobuf.WireFormat$JavaType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.FontFamily$Resolver getFontFamilyResolver()
androidx.compose.ui.platform.ViewLayerContainer: void dispatchGetDisplayList()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatTextView$SuperCaller getSuperCaller()
io.despicable.chromeos.domain.model.WeatherData$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType[] values()
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
io.despicable.chromeos.domain.model.IconSize: io.despicable.chromeos.domain.model.IconSize[] values()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver: ActionCallbackBroadcastReceiver()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setKeyListener(android.text.method.KeyListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.ui.platform.AndroidComposeView: boolean getShowLayoutBounds()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction[] values()
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
kotlin.DeprecationLevel: kotlin.DeprecationLevel[] values()
androidx.appcompat.widget.Toolbar$Api33Impl: void tryRegisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState[] values()
androidx.work.impl.diagnostics.DiagnosticsReceiver: DiagnosticsReceiver()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.glance.appwidget.proto.LayoutProto$DimensionType: androidx.glance.appwidget.proto.LayoutProto$DimensionType valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorStateList(android.widget.RemoteViews,int,java.lang.String,android.content.res.ColorStateList)
androidx.appcompat.widget.DropDownListView$Api21Impl: void drawableHotspotChanged(android.view.View,float,float)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.Clipboard getClipboard()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.appcompat.view.menu.ActionMenuItemView: java.lang.CharSequence getAccessibilityClassName()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.material.ripple.RippleHostView: void setRippleState(boolean)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.appcompat.widget.AppCompatTextView: void setEmojiCompatEnabled(boolean)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.glance.appwidget.action.ApplyActionApi31Impl: void setOnCheckedChangeResponse(android.widget.RemoteViews,int,android.app.PendingIntent)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithPresetSizes(android.widget.TextView,int[],int)
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection[] values()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.room.ObservedTableStates$ObserveOp: androidx.room.ObservedTableStates$ObserveOp[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.compose.ui.platform.AndroidComposeView: void setCoroutineContext(kotlin.coroutines.CoroutineContext)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus valueOf(java.lang.String)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.glance.appwidget.action.StrictModeVmPolicyApi31Impl: android.os.StrictMode$VmPolicy$Builder permitUnsafeIntentLaunch(android.os.StrictMode$VmPolicy$Builder)
androidx.glance.session.SessionWorker: SessionWorker(android.content.Context,androidx.work.WorkerParameters,androidx.glance.session.SessionManager,androidx.glance.session.TimeoutOptions,kotlinx.coroutines.CoroutineDispatcher)
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent valueOf(java.lang.String)
org.koin.core.logger.Level: org.koin.core.logger.Level valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorStateListAttr(android.widget.RemoteViews,int,java.lang.String,int)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.compose.ui.platform.AndroidComposeView: android.view.View getView()
androidx.compose.ui.platform.AndroidComposeView: android.view.View findViewByAccessibilityIdTraversal(int)
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType[] values()
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.compose.ui.platform.AndroidComposeView: void setShowLayoutBounds(boolean)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy: ConstraintProxy$StorageNotLowProxy()
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.WindowInfo getWindowInfo()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.Autofill getAutofill()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.glance.appwidget.RemoteViewsTranslatorApi31Impl: void addChildView(android.widget.RemoteViews,int,android.widget.RemoteViews,int)
androidx.compose.ui.graphics.layer.ViewLayer: boolean getCanUseCompositingLayer$ui_graphics_release()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setExitTransition(android.widget.PopupWindow,android.transition.Transition)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.work.impl.background.systemjob.SystemJobService: SystemJobService()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.glance.appwidget.action.ActionTrampolineActivity: ActionTrampolineActivity()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode valueOf(java.lang.String)
io.despicable.chromeos.WeatherApplication: WeatherApplication()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus[] values()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason valueOf(java.lang.String)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setIntDimenAttr(android.widget.RemoteViews,int,java.lang.String,int)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode valueOf(java.lang.String)
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight[] values()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getLayoutNodeToHolder()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.glance.session.SessionWorker: SessionWorker(android.content.Context,androidx.work.WorkerParameters,androidx.glance.session.SessionManager,androidx.glance.session.TimeoutOptions,kotlinx.coroutines.CoroutineDispatcher,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus[] values()
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidViewsHandler getAndroidViewsHandler$ui_release()
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand[] values()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.compose.ui.input.pointer.PointerInputEventHandler: java.lang.Object invoke(androidx.compose.ui.input.pointer.PointerInputScope,kotlin.coroutines.Continuation)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl: void setSystemUiVisibility(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.Density getDensity()
kotlin.DeprecationLevel: kotlin.DeprecationLevel valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: void setInsets(int,androidx.core.graphics.Insets)
androidx.glance.appwidget.proto.LayoutProto$ContentScale: androidx.glance.appwidget.proto.LayoutProto$ContentScale[] values()
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
io.despicable.chromeos.domain.model.WeatherCondition$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.graphics.AndroidPath_androidKt: void throwIllegalStateException(java.lang.String)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.compose.ui.platform.AndroidComposeView: kotlin.coroutines.CoroutineContext getCoroutineContext()
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.compose.ui.platform.AndroidComposeView: void setFontFamilyResolver(androidx.compose.ui.text.font.FontFamily$Resolver)
androidx.glance.appwidget.action.InvisibleActionTrampolineActivity: InvisibleActionTrampolineActivity()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.ui.platform.ComposeView: void setContent(kotlin.jvm.functions.Function2)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.compose.ui.platform.AbstractComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.compose.runtime.collection.MutableVectorKt: void throwListIndexOutOfBoundsException(int,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason[] values()
androidx.glance.appwidget.proto.LayoutProto$VerticalAlignment: androidx.glance.appwidget.proto.LayoutProto$VerticalAlignment[] values()
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.MutableIntObjectMap getLayoutNodes()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.appcompat.widget.DropDownListView$Api33Impl: boolean isSelectedChildViewEnabled(android.widget.AbsListView)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl[] values()
androidx.compose.ui.platform.DrawChildContainer: int getChildCount()
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.compose.ui.platform.AndroidComposeView: long getMeasureIteration()
org.koin.core.logger.Level: org.koin.core.logger.Level[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.glance.session.Api23Impl: boolean isIdle(android.os.PowerManager)
androidx.work.DirectExecutor: androidx.work.DirectExecutor[] values()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens valueOf(java.lang.String)
io.despicable.chromeos.MainActivity: MainActivity()
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.focus.FocusOwner getFocusOwner()
io.despicable.chromeos.domain.model.IconSize$Companion: kotlinx.serialization.KSerializer serializer()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.pointer.PointerIconService getPointerIconService()
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.room.Transactor$SQLiteTransactionType: androidx.room.Transactor$SQLiteTransactionType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: android.os.LocaleList forLanguageTags(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.glance.appwidget.proto.LayoutProto$HorizontalAlignment: androidx.glance.appwidget.proto.LayoutProto$HorizontalAlignment[] values()
androidx.appcompat.widget.Toolbar: android.view.View getNavButtonView()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
androidx.compose.ui.platform.AndroidComposeViewAssistHelperMethodsO: void setClassName(android.view.ViewStructure,android.view.View)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.contentcapture.AndroidContentCaptureManager getContentCaptureManager$ui_release()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand[] values()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.compose.ui.platform.ComposeView: void getShouldCreateCompositionOnAttachedToWindow$annotations()
androidx.compose.ui.platform.AbstractComposeView: boolean getShowLayoutBounds()
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsN: void setPointerIcon(android.view.View,androidx.compose.ui.input.pointer.PointerIcon)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.platform.AndroidComposeViewStartDragAndDropN: boolean startDragAndDrop(android.view.View,androidx.compose.ui.draganddrop.DragAndDropTransferData,androidx.compose.ui.draganddrop.ComposeDragShadowBuilder)
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.compose.ui.text.font.FontWeightAdjustmentHelperApi31: int fontWeightAdjustment(android.content.Context)
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNode getRoot()
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.DrawChildContainer getContainer()
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedDispatcher findOnBackInvokedDispatcher(android.view.View)
androidx.compose.ui.platform.ViewLayer: void setCameraDistancePx(float)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.compose.ui.platform.OutlineVerificationHelper: void setPath(android.graphics.Outline,androidx.compose.ui.graphics.Path)
androidx.glance.appwidget.protobuf.JavaType: androidx.glance.appwidget.protobuf.JavaType valueOf(java.lang.String)
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.activity.EdgeToEdgeApi26: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.compose.ui.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority[] values()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.compose.ui.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setEmojiCompatEnabled(boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api23Impl: android.text.StaticLayout createStaticLayoutForMeasuring(java.lang.CharSequence,android.text.Layout$Alignment,int,int,android.widget.TextView,android.text.TextPaint,androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setCharSequenceAttr(android.widget.RemoteViews,int,java.lang.String,int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setCharSequence(android.widget.RemoteViews,int,java.lang.String,int)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
androidx.glance.Visibility: androidx.glance.Visibility valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.compose.ui.platform.AndroidComposeView: boolean getHasPendingMeasureOrLayout()
androidx.compose.animation.core.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State valueOf(java.lang.String)
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.semantics.SemanticsOwner getSemanticsOwner()
androidx.work.impl.WorkDatabase_Impl: WorkDatabase_Impl()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorSize(long)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.glance.appwidget.LayoutSize: androidx.glance.appwidget.LayoutSize valueOf(java.lang.String)
androidx.glance.appwidget.RemoteViewsTranslatorApi28Impl: android.widget.RemoteViews copyRemoteViews(android.widget.RemoteViews)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.compose.ui.graphics.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.graphics.GraphicsContext getGraphicsContext()
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult[] values()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState valueOf(java.lang.String)
androidx.room.ObservedTableStates$ObserveOp: androidx.room.ObservedTableStates$ObserveOp valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.glance.appwidget.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.glance.appwidget.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State valueOf(java.lang.String)
androidx.compose.material.ripple.RippleHostView: void setRippleState$lambda$2(androidx.compose.material.ripple.RippleHostView)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.glance.appwidget.ApplyModifiersApi31Impl: void setViewWidth(android.widget.RemoteViews,int,androidx.glance.unit.Dimension)
androidx.graphics.path.PathIteratorPreApi34Impl: long createInternalPathIterator(android.graphics.Path,int,float)
androidx.compose.ui.platform.AndroidComposeView: void getShowLayoutBounds$annotations()
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorStateList(android.widget.RemoteViews,int,java.lang.String,int)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.compose.ui.unit.ConstraintsKt: void throwInvalidConstraintException(int,int)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorStateList(android.widget.RemoteViews,int,java.lang.String,android.content.res.ColorStateList,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy[] values()
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.appcompat.widget.AppCompatImageView: void setImageLevel(int)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.graphics.Path getManualClipPath()
androidx.graphics.path.ConicConverter: int internalConicToQuadratics(float[],int,float[],float,float)
androidx.work.NetworkType: androidx.work.NetworkType[] values()
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State[] values()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent[] values()
androidx.appcompat.widget.AppCompatImageButton: void setImageLevel(int)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.glance.Visibility: androidx.glance.Visibility[] values()
androidx.glance.session.Api33Impl: boolean isLightIdleOrLowPowerStandby(android.os.PowerManager)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection valueOf(java.lang.String)
androidx.appcompat.widget.ListPopupWindow$Api24Impl: int getMaxAvailableHeight(android.widget.PopupWindow,android.view.View,int,boolean)
androidx.glance.appwidget.Api31Impl: android.widget.RemoteViews createRemoteViews(java.util.Map)
androidx.glance.appwidget.protobuf.WireFormat$FieldType: androidx.glance.appwidget.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.compose.runtime.collection.MutableVectorKt: void throwReversedIndicesException(int,int)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ViewConfiguration getViewConfiguration()
androidx.compose.ui.platform.ComposeView: java.lang.CharSequence getAccessibilityClassName()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.compose.ui.platform.AbstractComposeView: void setParentCompositionContext(androidx.compose.runtime.CompositionContext)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.compose.ui.platform.ViewLayer: float[] getUnderlyingMatrix-sQKQjiQ()
androidx.compose.ui.util.ListUtilsKt: void throwUnsupportedOperationException(java.lang.String)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.Font$ResourceLoader getFontLoader()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.glance.appwidget.proto.LayoutProto$VerticalAlignment: androidx.glance.appwidget.proto.LayoutProto$VerticalAlignment valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNodeDrawScope getSharedDrawScope()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.compose.ui.graphics.layer.ViewLayer: void setCanUseCompositingLayer$ui_graphics_release(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.work.impl.WorkDatabase: WorkDatabase()
androidx.work.CoroutineWorker: CoroutineWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: void setInsets(int,androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.glance.appwidget.LayoutType: androidx.glance.appwidget.LayoutType[] values()
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: int getAutoSizeStepGranularity(android.widget.TextView)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.compose.runtime.PreconditionsKt: void throwIllegalStateException(java.lang.String)
androidx.glance.appwidget.protobuf.JavaType: androidx.glance.appwidget.protobuf.JavaType[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ClipboardManager getClipboardManager()
androidx.core.view.WindowInsetsCompat$Impl20: boolean systemBarVisibilityEquals(int,int)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode[] values()
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.appcompat.widget.SearchView$Api29Impl: void refreshAutoCompleteResults(android.widget.AutoCompleteTextView)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver: ForceStopRunnable$BroadcastReceiver()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState valueOf(java.lang.String)
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity: WidgetConfigActivity()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorNext(long,float[],int)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax[] values()
androidx.glance.appwidget.proto.LayoutProto$NodeIdentity: androidx.glance.appwidget.proto.LayoutProto$NodeIdentity valueOf(java.lang.String)
androidx.glance.appwidget.proto.LayoutProto$HorizontalAlignment: androidx.glance.appwidget.proto.LayoutProto$HorizontalAlignment valueOf(java.lang.String)
androidx.glance.appwidget.LayoutSelectionApi31Impl: android.widget.RemoteViews remoteViews(java.lang.String,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.core.widget.RemoteViewsCompat$Api31Impl: void setColorAttr(android.widget.RemoteViews,int,java.lang.String,int)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.AndroidDragAndDropManager getDragAndDropManager()
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.compose.runtime.ComposerKt: void composeImmediateRuntimeError(java.lang.String)
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getHolderToLayoutNode()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.compose.ui.platform.MotionEventVerifierApi29: boolean isValidMotionEvent(android.view.MotionEvent,int)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.core.view.ViewCompat$Api30Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.InputModeManager getInputModeManager()
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.glance.appwidget.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.glance.appwidget.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName[] values()
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode[] values()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.widget.SearchView$Api29Impl: void setInputMethodMode(androidx.appcompat.widget.SearchView$SearchAutoComplete,int)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.view.WindowInsetsCompat$Impl34: boolean isVisible(int)
androidx.room.Transactor$SQLiteTransactionType: androidx.room.Transactor$SQLiteTransactionType[] values()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
