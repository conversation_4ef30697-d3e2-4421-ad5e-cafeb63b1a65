name: "Setup Android Build"
description: "Common steps before running a Gradle command"

runs:
  using: "composite"
  steps:
    - name: Setup JDK
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: 21

    - name: Setup Gradle
      uses: gradle/actions/setup-gradle@v4
      with:
        add-job-summary: never

    - name: Optimize for Gradle build
      shell: bash
      run: ${{ github.action_path }}/setup-gradle-properties.sh
