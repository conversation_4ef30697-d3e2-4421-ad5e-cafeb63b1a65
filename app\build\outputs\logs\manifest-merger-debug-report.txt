-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:2:1-53:12
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39208631b02ee562008e36749252cf44\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5a5ac5787a9ad7f21a44018aa6dab8\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3832d2c8f326ec4187ac4a1d32501c91\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4b440f75078133df6ad6510cdc5bb4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47301a4e8af2ebaaa3063e6479fbc928\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1545388fb70c41300643a5971266c0b\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7e1a03de3e844a22441a37d5a0c3a8\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e4ab69e1da09d111257f580b63f8e31\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73471e8268cfdfdc3680c52ecc7020\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc9cb04f6aac2669ae6fa2db8935d260\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58cf529ee9bcb6985359e9d96cd95a3a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39babb803641efb4abb931d2a7a5d339\transformed\glance-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:17:1-61:12
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b6b566591c56e227453dc103f893054\transformed\glance-material3-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c91fc286a144f7dd3fed97bfa6a44d86\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a78baa1dd3b10408707cac07e3dcb302\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:2:1-7:12
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28b430625e95313c16f6ec8453ea0b40\transformed\reorderable-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4147f5993b357fe1d0697c3486956871\transformed\aboutlibraries-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\834bf615f3749af516fa6fc397086833\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa607cf7e6bb1da5c7d21ce61c034c9c\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2431c590a4af45b7a501b8b3a1c4a590\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e1d3faf204d8dc115e2ef9096d86cb\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91ac8cdd8a96ddf48c8d50b3af514b54\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d6f01189cd3d96670bca273ce5a2082\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e70223b363bd5f99b039ada74af213\transformed\ui-backhandler-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\289686107515fa75e5758775a2ecfe1c\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b4fd7fcef298b41f39dafcff5eb19bb\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b5e08ae6a38078aa05566caa111bb4f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e745d6608c68c15a01529d144dc6b8c\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e3cc776ddb8090a4936e666954523f6\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7f4358a560a3680df65cb14f7c0817b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7447cd5c348eea0fe1634dc086b66f6b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecc1c2981ed32ff04a344f0f0ac80de2\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df402849ddc0f9e7f1a0619c5d8094da\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057c67d4fe2ed6b6fe8bb62a6954b32d\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7308887013a2be47fde69de0853200e\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e8aca9b8f5a0b62b3b875c0d203bd1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7459399df2d5d90f083c2a80e08ee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25663c94ddc73789937d3a1ee64b0b07\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aabc9fb396aa0e7b8f5b63a834da2bdd\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1574cd25f0613d1e447c947dbcccd8aa\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809af1d6ad911492049172922dbfa13f\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd9aa59b601d7b5b4fa3318dac2a9fd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7613315b5faa388b6f97244de915db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4cda80c164b28eda1339d4420cadeea\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d80ddf48e8b7a7ef835f9f399aae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ee674f951387fdad8e027553435e743\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43074edfa33b3bb9c2133a6549103b9d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a87736fc935020070d380641e9b83fa\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8789f97de103d1b7eb8a581f0f0be62f\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2694e11f3e1288e10fd8bc7bc531d35\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316788cae763d29a5e4bef4741d327c3\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0d999e9958faa1e2bd73fcbc40c54a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19877fa616da5a98a8488ccaa7bab822\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46a005f7db48e5818730cc8ce8de261\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9de44a3d2980beab9460cb55354ba2\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1faf5a170f74800c73eaede5aace2a36\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ea2ee4009397bd80cf92b97630d3a18\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6be99e8ef4ebb99e2a8673e7c3bd48fe\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d531cb9ac8d38fdf826cdbc25dccf69e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e916fe6b5dc393a134112db8a47cfd2\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10fa6fd426766e10ee7854512001ba3d\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49cdc21a8cd3e0df61b3ff37e4cf8ea4\transformed\core-bundle-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52981bd7c16503ab36565c23f35603a4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da95d32215f7265175208a51488cbca8\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d27ebbc30fdc3e519f7a53d3f50f6e1\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acff7f57bc3a7b0b7a9c3a6fcde4195\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6785009020cf6f655fa23647c70ce01f\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8a76b5d545c6e3de73217e505982d96\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\314f2bb57d677f489396bb6ceb95130d\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c47c2f0d707a77500de5d16603646604\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65446332b6e7655e06a8244a1908e386\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.mikepenz:aboutlibraries-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b555e4b9aa1f401e3e4ce1874d2684c\transformed\aboutlibraries-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaf76573ae95560643cd0ec6870fd644\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30c45852bec890e2b4f57334c9f61253\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab01b3490380ba3fd94dd806914b1fa3\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df142642591b39aa45c9434828573233\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df7267b64c2269cb11b82c5df7472cfe\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b7555004e4ced5c70f95030b892b91\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0cacda2f573a4bd19f2f5f7856b2bea\transformed\core-uri-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44f61f7ff6723e3d17f6d5d34eae84b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff94553f33284d8531948ffac8ad1344\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c307f8b36f787856618f98680b7fb881\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.performance:performance-annotation-android:1.0.0-alpha01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc03fa3297ec9632d18b8ca726fadd49\transformed\performance-annotation-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197f7c6ddef0d1025c3a00e18d31a93f\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:5:5-51:19
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:5:5-51:19
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\AndroidManifest.xml:7:5-20
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:22:5-59:19
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:22:5-59:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:12:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:15:9-29
	android:icon
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:14:9-46
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:6:9-43
activity#io.despicable.chromeos.MainActivity
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:16:9-26:20
	android:label
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:19:13-45
	android:exported
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:20:13-50
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:27-74
activity#io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:29:9-36:20
	android:exported
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:31:13-37
	android:theme
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:32:13-50
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:30:13-72
intent-filter#action:name:android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:33:13-35:29
action#android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:17-87
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:25-84
receiver#io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:39:9-50:20
	android:exported
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:41:13-36
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:40:13-73
intent-filter#action:name:android.appwidget.action.APPWIDGET_DELETED+action:name:android.appwidget.action.APPWIDGET_UPDATE+action:name:io.despicable.chromeos.ACTION_UPDATE_WEATHER
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:42:13-46:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:17-84
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:25-81
action#android.appwidget.action.APPWIDGET_DELETED
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:17-85
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:25-82
action#io.despicable.chromeos.ACTION_UPDATE_WEATHER
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:17-87
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:25-84
meta-data#android.appwidget.provider
ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:47:13-49:63
	android:resource
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:49:17-60
	android:name
		ADDED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:48:17-58
uses-sdk
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39208631b02ee562008e36749252cf44\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39208631b02ee562008e36749252cf44\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5a5ac5787a9ad7f21a44018aa6dab8\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5a5ac5787a9ad7f21a44018aa6dab8\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3832d2c8f326ec4187ac4a1d32501c91\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3832d2c8f326ec4187ac4a1d32501c91\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4b440f75078133df6ad6510cdc5bb4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4b440f75078133df6ad6510cdc5bb4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47301a4e8af2ebaaa3063e6479fbc928\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47301a4e8af2ebaaa3063e6479fbc928\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1545388fb70c41300643a5971266c0b\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1545388fb70c41300643a5971266c0b\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7e1a03de3e844a22441a37d5a0c3a8\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7e1a03de3e844a22441a37d5a0c3a8\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e4ab69e1da09d111257f580b63f8e31\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e4ab69e1da09d111257f580b63f8e31\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73471e8268cfdfdc3680c52ecc7020\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73471e8268cfdfdc3680c52ecc7020\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc9cb04f6aac2669ae6fa2db8935d260\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc9cb04f6aac2669ae6fa2db8935d260\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58cf529ee9bcb6985359e9d96cd95a3a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58cf529ee9bcb6985359e9d96cd95a3a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39babb803641efb4abb931d2a7a5d339\transformed\glance-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39babb803641efb4abb931d2a7a5d339\transformed\glance-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b6b566591c56e227453dc103f893054\transformed\glance-material3-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b6b566591c56e227453dc103f893054\transformed\glance-material3-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c91fc286a144f7dd3fed97bfa6a44d86\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c91fc286a144f7dd3fed97bfa6a44d86\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a78baa1dd3b10408707cac07e3dcb302\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a78baa1dd3b10408707cac07e3dcb302\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28b430625e95313c16f6ec8453ea0b40\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [sh.calvin.reorderable:reorderable-android-debug:2.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28b430625e95313c16f6ec8453ea0b40\transformed\reorderable-debug\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4147f5993b357fe1d0697c3486956871\transformed\aboutlibraries-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4147f5993b357fe1d0697c3486956871\transformed\aboutlibraries-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\834bf615f3749af516fa6fc397086833\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\834bf615f3749af516fa6fc397086833\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa607cf7e6bb1da5c7d21ce61c034c9c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa607cf7e6bb1da5c7d21ce61c034c9c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2431c590a4af45b7a501b8b3a1c4a590\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2431c590a4af45b7a501b8b3a1c4a590\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e1d3faf204d8dc115e2ef9096d86cb\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e1d3faf204d8dc115e2ef9096d86cb\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91ac8cdd8a96ddf48c8d50b3af514b54\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91ac8cdd8a96ddf48c8d50b3af514b54\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d6f01189cd3d96670bca273ce5a2082\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d6f01189cd3d96670bca273ce5a2082\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e70223b363bd5f99b039ada74af213\transformed\ui-backhandler-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e70223b363bd5f99b039ada74af213\transformed\ui-backhandler-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\289686107515fa75e5758775a2ecfe1c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\289686107515fa75e5758775a2ecfe1c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b4fd7fcef298b41f39dafcff5eb19bb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b4fd7fcef298b41f39dafcff5eb19bb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b5e08ae6a38078aa05566caa111bb4f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b5e08ae6a38078aa05566caa111bb4f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e745d6608c68c15a01529d144dc6b8c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e745d6608c68c15a01529d144dc6b8c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e3cc776ddb8090a4936e666954523f6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e3cc776ddb8090a4936e666954523f6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7f4358a560a3680df65cb14f7c0817b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7f4358a560a3680df65cb14f7c0817b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7447cd5c348eea0fe1634dc086b66f6b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7447cd5c348eea0fe1634dc086b66f6b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecc1c2981ed32ff04a344f0f0ac80de2\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecc1c2981ed32ff04a344f0f0ac80de2\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df402849ddc0f9e7f1a0619c5d8094da\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df402849ddc0f9e7f1a0619c5d8094da\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057c67d4fe2ed6b6fe8bb62a6954b32d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057c67d4fe2ed6b6fe8bb62a6954b32d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7308887013a2be47fde69de0853200e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7308887013a2be47fde69de0853200e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e8aca9b8f5a0b62b3b875c0d203bd1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e8aca9b8f5a0b62b3b875c0d203bd1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7459399df2d5d90f083c2a80e08ee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7459399df2d5d90f083c2a80e08ee\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25663c94ddc73789937d3a1ee64b0b07\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25663c94ddc73789937d3a1ee64b0b07\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aabc9fb396aa0e7b8f5b63a834da2bdd\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aabc9fb396aa0e7b8f5b63a834da2bdd\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1574cd25f0613d1e447c947dbcccd8aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1574cd25f0613d1e447c947dbcccd8aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809af1d6ad911492049172922dbfa13f\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809af1d6ad911492049172922dbfa13f\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd9aa59b601d7b5b4fa3318dac2a9fd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd9aa59b601d7b5b4fa3318dac2a9fd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7613315b5faa388b6f97244de915db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7613315b5faa388b6f97244de915db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4cda80c164b28eda1339d4420cadeea\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4cda80c164b28eda1339d4420cadeea\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d80ddf48e8b7a7ef835f9f399aae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d80ddf48e8b7a7ef835f9f399aae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ee674f951387fdad8e027553435e743\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ee674f951387fdad8e027553435e743\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43074edfa33b3bb9c2133a6549103b9d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43074edfa33b3bb9c2133a6549103b9d\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a87736fc935020070d380641e9b83fa\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a87736fc935020070d380641e9b83fa\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8789f97de103d1b7eb8a581f0f0be62f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8789f97de103d1b7eb8a581f0f0be62f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2694e11f3e1288e10fd8bc7bc531d35\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2694e11f3e1288e10fd8bc7bc531d35\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316788cae763d29a5e4bef4741d327c3\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316788cae763d29a5e4bef4741d327c3\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0d999e9958faa1e2bd73fcbc40c54a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0d999e9958faa1e2bd73fcbc40c54a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19877fa616da5a98a8488ccaa7bab822\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19877fa616da5a98a8488ccaa7bab822\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46a005f7db48e5818730cc8ce8de261\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46a005f7db48e5818730cc8ce8de261\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9de44a3d2980beab9460cb55354ba2\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9de44a3d2980beab9460cb55354ba2\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1faf5a170f74800c73eaede5aace2a36\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1faf5a170f74800c73eaede5aace2a36\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ea2ee4009397bd80cf92b97630d3a18\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ea2ee4009397bd80cf92b97630d3a18\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6be99e8ef4ebb99e2a8673e7c3bd48fe\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6be99e8ef4ebb99e2a8673e7c3bd48fe\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d531cb9ac8d38fdf826cdbc25dccf69e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d531cb9ac8d38fdf826cdbc25dccf69e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e916fe6b5dc393a134112db8a47cfd2\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e916fe6b5dc393a134112db8a47cfd2\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10fa6fd426766e10ee7854512001ba3d\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10fa6fd426766e10ee7854512001ba3d\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49cdc21a8cd3e0df61b3ff37e4cf8ea4\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49cdc21a8cd3e0df61b3ff37e4cf8ea4\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52981bd7c16503ab36565c23f35603a4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52981bd7c16503ab36565c23f35603a4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da95d32215f7265175208a51488cbca8\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da95d32215f7265175208a51488cbca8\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d27ebbc30fdc3e519f7a53d3f50f6e1\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d27ebbc30fdc3e519f7a53d3f50f6e1\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acff7f57bc3a7b0b7a9c3a6fcde4195\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acff7f57bc3a7b0b7a9c3a6fcde4195\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6785009020cf6f655fa23647c70ce01f\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6785009020cf6f655fa23647c70ce01f\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8a76b5d545c6e3de73217e505982d96\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8a76b5d545c6e3de73217e505982d96\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\314f2bb57d677f489396bb6ceb95130d\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\314f2bb57d677f489396bb6ceb95130d\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c47c2f0d707a77500de5d16603646604\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c47c2f0d707a77500de5d16603646604\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65446332b6e7655e06a8244a1908e386\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65446332b6e7655e06a8244a1908e386\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b555e4b9aa1f401e3e4ce1874d2684c\transformed\aboutlibraries-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-core-android:12.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b555e4b9aa1f401e3e4ce1874d2684c\transformed\aboutlibraries-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaf76573ae95560643cd0ec6870fd644\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaf76573ae95560643cd0ec6870fd644\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30c45852bec890e2b4f57334c9f61253\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30c45852bec890e2b4f57334c9f61253\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab01b3490380ba3fd94dd806914b1fa3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab01b3490380ba3fd94dd806914b1fa3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df142642591b39aa45c9434828573233\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df142642591b39aa45c9434828573233\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df7267b64c2269cb11b82c5df7472cfe\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df7267b64c2269cb11b82c5df7472cfe\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b7555004e4ced5c70f95030b892b91\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b7555004e4ced5c70f95030b892b91\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0cacda2f573a4bd19f2f5f7856b2bea\transformed\core-uri-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0cacda2f573a4bd19f2f5f7856b2bea\transformed\core-uri-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44f61f7ff6723e3d17f6d5d34eae84b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44f61f7ff6723e3d17f6d5d34eae84b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff94553f33284d8531948ffac8ad1344\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff94553f33284d8531948ffac8ad1344\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c307f8b36f787856618f98680b7fb881\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c307f8b36f787856618f98680b7fb881\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.performance:performance-annotation-android:1.0.0-alpha01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc03fa3297ec9632d18b8ca726fadd49\transformed\performance-annotation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.performance:performance-annotation-android:1.0.0-alpha01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc03fa3297ec9632d18b8ca726fadd49\transformed\performance-annotation-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197f7c6ddef0d1025c3a00e18d31a93f\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197f7c6ddef0d1025c3a00e18d31a93f\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml
activity#androidx.glance.appwidget.action.ActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
	android:excludeFromRecents
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
activity#androidx.glance.appwidget.action.InvisibleActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
	android:launchMode
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
	android:noHistory
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
	android:taskAffinity
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
receiver#androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
receiver#androidx.glance.appwidget.UnmanagedSessionReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
receiver#androidx.glance.appwidget.MyPackageReplacedReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
service#androidx.glance.appwidget.GlanceRemoteViewsService
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
	android:permission
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
service#androidx.core.widget.RemoteViewsCompatService
ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
	tools:ignore
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:27:13-60
	android:permission
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
	android:name
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
