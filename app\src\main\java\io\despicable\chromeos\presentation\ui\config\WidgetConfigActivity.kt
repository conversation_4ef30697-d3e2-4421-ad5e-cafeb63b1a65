package io.despicable.chromeos.presentation.ui.config

import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import androidx.glance.appwidget.updateAll
import androidx.lifecycle.lifecycleScope
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase
import io.despicable.chromeos.presentation.ui.widget.WeatherWidget
import io.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel
import io.despicable.chromeos.ui.theme.ChromeOSTheme
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.core.parameter.parametersOf

/**
 * Configuration activity for weather widget
 */
class WidgetConfigActivity : ComponentActivity() {

    companion object {
        private const val TAG = "WidgetConfigActivity"
    }

    private val getWeatherDataUseCase: GetWeatherDataUseCase by inject()
    private val updateWidgetConfigUseCase: UpdateWidgetConfigUseCase by inject()

    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate() called")
        enableEdgeToEdge()

        // Get widget ID from intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        Log.d(TAG, "Widget ID: $appWidgetId")

        // If widget ID is invalid, finish activity
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            Log.e(TAG, "Invalid widget ID, finishing activity")
            finish()
            return
        }

        // Set result to CANCELED initially
        setResult(RESULT_CANCELED)
        Log.d(TAG, "Initial result set to CANCELED")

        // Create ViewModel using Koin
        Log.d(TAG, "Creating ViewModel with widget ID: $appWidgetId")
        val viewModel: WidgetConfigViewModel by inject { parametersOf(appWidgetId) }
        Log.d(TAG, "ViewModel created successfully")

        setContent {
            Log.d(TAG, "Setting content")
            ChromeOSTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    WidgetConfigScreen(
                        viewModel = viewModel,
                        onSaveClicked = {
                            Log.d(TAG, "onSaveClicked callback triggered")
                            saveConfigurationAndFinish()
                        },
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    private fun saveConfigurationAndFinish() {
        Log.d(TAG, "saveConfigurationAndFinish() called")
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Updating widget...")
                // Update the widget
                WeatherWidget().updateAll(this@WidgetConfigActivity)
                Log.d(TAG, "Widget updated successfully")

                // Set result to OK
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(RESULT_OK, resultValue)
                Log.d(TAG, "Result set to OK with widget ID: $appWidgetId")

                finish()
                Log.d(TAG, "Activity finished")
            } catch (e: Exception) {
                // Handle error
                Log.e(TAG, "Error in saveConfigurationAndFinish", e)
                e.printStackTrace()
                finish()
            }
        }
    }
}
