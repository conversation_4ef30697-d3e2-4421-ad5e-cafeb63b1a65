<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@android:id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    tools:layout_height="400dp"
    tools:layout_width="400dp"
    tools:targetApi="s">

    <ImageView
        android:id="@+id/iv_placeholder"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:importantForAccessibility="no"
        android:src="@drawable/ic_hourglass"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_widget"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/photo_widget_cd_host"
        tools:scaleType="centerCrop"
        tools:src="@drawable/widget_preview" />

    <ImageView
        android:id="@+id/iv_widget_fill"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/photo_widget_cd_host"
        android:scaleType="centerCrop"
        android:visibility="gone"
        tools:src="@drawable/widget_preview" />

    <LinearLayout
        android:id="@+id/tap_actions_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/view_tap_left"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3" />

        <FrameLayout
            android:id="@+id/view_tap_center"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="4" />

        <FrameLayout
            android:id="@+id/view_tap_right"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3" />
    </LinearLayout>
</FrameLayout>
