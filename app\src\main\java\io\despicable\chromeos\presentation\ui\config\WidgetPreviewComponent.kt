package io.despicable.chromeos.presentation.ui.config

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import io.despicable.chromeos.domain.model.WeatherCondition
import io.despicable.chromeos.domain.model.WeatherData
import io.despicable.chromeos.domain.model.WidgetConfiguration

/**
 * Real-time preview component that mimics the actual widget appearance
 */
@Composable
fun WidgetPreviewComponent(
    weatherData: WeatherData?,
    configuration: WidgetConfiguration,
    modifier: Modifier = Modifier
) {
    val TAG = "WidgetPreviewComponent"
    Log.d(TAG, "Rendering preview with weatherData: $weatherData")
    Log.d(TAG, "Configuration: $configuration")
    Card(
        modifier = modifier
            .size(200.dp, 160.dp)
            .clip(RoundedCornerShape(16.dp))
            .border(
                width = 2.dp,
                color = MaterialTheme.colorScheme.outline,
                shape = RoundedCornerShape(16.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            // Always show content, use fallback data if needed
            val displayData = weatherData ?: run {
                Log.d(TAG, "No weather data, using fallback for city: ${configuration.cityName}")
                io.despicable.chromeos.util.SampleWeatherData.generateRandomWeatherData(
                    configuration.cityName.ifEmpty { "Sample City" }
                )
            }

            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Weather icon (emoji)
                Text(
                    text = getWeatherEmoji(displayData.weatherCondition),
                    fontSize = configuration.iconSize.sizeDp.sp,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Temperature
                Text(
                    text = "${displayData.temperature}°C",
                    fontSize = (configuration.fontSize.sizeSp + 4).sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(4.dp))

                // City name
                Text(
                    text = displayData.cityName,
                    fontSize = configuration.fontSize.sizeSp.sp,
                    color = Color.DarkGray,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )

                if (configuration.showHumidity || configuration.showWindSpeed) {
                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (configuration.showHumidity) {
                            Text(
                                text = "💧${displayData.humidity}%",
                                fontSize = (configuration.fontSize.sizeSp - 2).sp,
                                color = Color.Gray
                            )

                            if (configuration.showWindSpeed) {
                                Spacer(modifier = Modifier.width(12.dp))
                            }
                        }

                        if (configuration.showWindSpeed) {
                            Text(
                                text = "💨${String.format("%.1f", displayData.windSpeed)}km/h",
                                fontSize = (configuration.fontSize.sizeSp - 2).sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Get emoji representation for weather condition
 */
private fun getWeatherEmoji(condition: WeatherCondition): String {
    return when (condition) {
        WeatherCondition.SUNNY -> "☀️"
        WeatherCondition.CLOUDY -> "☁️"
        WeatherCondition.RAINY -> "🌧️"
        WeatherCondition.SNOWY -> "❄️"
        WeatherCondition.STORMY -> "⛈️"
        WeatherCondition.FOGGY -> "🌫️"
        WeatherCondition.PARTLY_CLOUDY -> "⛅"
    }
}
