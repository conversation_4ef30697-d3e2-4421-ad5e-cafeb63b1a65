  Activity android.app  Context android.content  Intent android.content  Build 
android.os  Bundle 
android.os  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
setContent androidx.activity.compose  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Boolean "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  FontSize "androidx.compose.foundation.layout  IconSize "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  WeatherData "androidx.compose.foundation.layout  WidgetConfigViewModel "androidx.compose.foundation.layout  WidgetConfiguration "androidx.compose.foundation.layout  io "androidx.compose.foundation.layout  
despicable %androidx.compose.foundation.layout.io  chromeos 0androidx.compose.foundation.layout.io.despicable  domain 9androidx.compose.foundation.layout.io.despicable.chromeos  model @androidx.compose.foundation.layout.io.despicable.chromeos.domain  WeatherData Fandroidx.compose.foundation.layout.io.despicable.chromeos.domain.model  WidgetConfiguration Fandroidx.compose.foundation.layout.io.despicable.chromeos.domain.model  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  RoundedCornerShape !androidx.compose.foundation.shape  Boolean androidx.compose.material3  
Composable androidx.compose.material3  Float androidx.compose.material3  FontSize androidx.compose.material3  IconSize androidx.compose.material3  List androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  Preview androidx.compose.material3  String androidx.compose.material3  Unit androidx.compose.material3  WeatherData androidx.compose.material3  WidgetConfigViewModel androidx.compose.material3  WidgetConfiguration androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  io androidx.compose.material3  lightColorScheme androidx.compose.material3  
despicable androidx.compose.material3.io  chromeos (androidx.compose.material3.io.despicable  domain 1androidx.compose.material3.io.despicable.chromeos  model 8androidx.compose.material3.io.despicable.chromeos.domain  WeatherData >androidx.compose.material3.io.despicable.chromeos.domain.model  WidgetConfiguration >androidx.compose.material3.io.despicable.chromeos.domain.model  Boolean androidx.compose.runtime  
Composable androidx.compose.runtime  Float androidx.compose.runtime  FontSize androidx.compose.runtime  IconSize androidx.compose.runtime  List androidx.compose.runtime  Modifier androidx.compose.runtime  OptIn androidx.compose.runtime  String androidx.compose.runtime  Unit androidx.compose.runtime  WidgetConfigViewModel androidx.compose.runtime  io androidx.compose.runtime  
despicable androidx.compose.runtime.io  chromeos &androidx.compose.runtime.io.despicable  domain /androidx.compose.runtime.io.despicable.chromeos  model 6androidx.compose.runtime.io.despicable.chromeos.domain  WeatherData <androidx.compose.runtime.io.despicable.chromeos.domain.model  WidgetConfiguration <androidx.compose.runtime.io.despicable.chromeos.domain.model  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  Role androidx.compose.ui.semantics  
FontWeight androidx.compose.ui.text.font  	TextAlign androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	DataStore androidx.datastore.core  preferencesDataStore androidx.datastore.preferences  Boolean #androidx.datastore.preferences.core  Context #androidx.datastore.preferences.core  Flow #androidx.datastore.preferences.core  Inject #androidx.datastore.preferences.core  Int #androidx.datastore.preferences.core  	Singleton #androidx.datastore.preferences.core  WidgetConfiguration #androidx.datastore.preferences.core  GlanceId androidx.glance  GlanceModifier androidx.glance  GlanceTheme androidx.glance  
background androidx.glance  GlanceAppWidget androidx.glance.appwidget  provideContent androidx.glance.appwidget  
Composable androidx.glance.layout  GlanceAppWidget androidx.glance.layout  WeatherData androidx.glance.layout  WidgetConfiguration androidx.glance.layout  
FontWeight androidx.glance.text  Text androidx.glance.text  	TextStyle androidx.glance.text  
ColorProvider androidx.glance.unit  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  EntityInsertAdapter 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  InvalidationTracker 
androidx.room  List 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  String 
androidx.room  
WeatherEntity 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Context androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
WeatherDao androidx.room.RoomDatabase  WeatherDatabase androidx.room.RoomDatabase  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  
Composable io.despicable.chromeos  Modifier io.despicable.chromeos  Preview io.despicable.chromeos  Unit io.despicable.chromeos  Context $io.despicable.chromeos.data.database  Database $io.despicable.chromeos.data.database  	Generated $io.despicable.chromeos.data.database  RoomDatabase $io.despicable.chromeos.data.database  Suppress $io.despicable.chromeos.data.database  Volatile $io.despicable.chromeos.data.database  
WeatherDao $io.despicable.chromeos.data.database  WeatherDatabase $io.despicable.chromeos.data.database  
WeatherEntity $io.despicable.chromeos.data.database  Context 4io.despicable.chromeos.data.database.WeatherDatabase  Volatile 4io.despicable.chromeos.data.database.WeatherDatabase  
WeatherDao 4io.despicable.chromeos.data.database.WeatherDatabase  WeatherDatabase 4io.despicable.chromeos.data.database.WeatherDatabase  Dao (io.despicable.chromeos.data.database.dao  Flow (io.despicable.chromeos.data.database.dao  	Generated (io.despicable.chromeos.data.database.dao  Insert (io.despicable.chromeos.data.database.dao  Int (io.despicable.chromeos.data.database.dao  List (io.despicable.chromeos.data.database.dao  OnConflictStrategy (io.despicable.chromeos.data.database.dao  Query (io.despicable.chromeos.data.database.dao  String (io.despicable.chromeos.data.database.dao  Suppress (io.despicable.chromeos.data.database.dao  
WeatherDao (io.despicable.chromeos.data.database.dao  WeatherDao_Impl (io.despicable.chromeos.data.database.dao  
WeatherEntity (io.despicable.chromeos.data.database.dao  OnConflictStrategy 3io.despicable.chromeos.data.database.dao.WeatherDao  Double +io.despicable.chromeos.data.database.entity  Entity +io.despicable.chromeos.data.database.entity  Int +io.despicable.chromeos.data.database.entity  Long +io.despicable.chromeos.data.database.entity  
PrimaryKey +io.despicable.chromeos.data.database.entity  String +io.despicable.chromeos.data.database.entity  
WeatherEntity +io.despicable.chromeos.data.database.entity  
toDomainModel +io.despicable.chromeos.data.database.entity  toEntity +io.despicable.chromeos.data.database.entity  Boolean %io.despicable.chromeos.data.datastore  Context %io.despicable.chromeos.data.datastore  Flow %io.despicable.chromeos.data.datastore  Inject %io.despicable.chromeos.data.datastore  Int %io.despicable.chromeos.data.datastore  	Singleton %io.despicable.chromeos.data.datastore  WidgetConfiguration %io.despicable.chromeos.data.datastore  WidgetPreferences %io.despicable.chromeos.data.datastore  Boolean 7io.despicable.chromeos.data.datastore.WidgetPreferences  Context 7io.despicable.chromeos.data.datastore.WidgetPreferences  Flow 7io.despicable.chromeos.data.datastore.WidgetPreferences  Inject 7io.despicable.chromeos.data.datastore.WidgetPreferences  Int 7io.despicable.chromeos.data.datastore.WidgetPreferences  WidgetConfiguration 7io.despicable.chromeos.data.datastore.WidgetPreferences  Boolean &io.despicable.chromeos.data.repository  Flow &io.despicable.chromeos.data.repository  Inject &io.despicable.chromeos.data.repository  Int &io.despicable.chromeos.data.repository  List &io.despicable.chromeos.data.repository  	Singleton &io.despicable.chromeos.data.repository  String &io.despicable.chromeos.data.repository  
WeatherDao &io.despicable.chromeos.data.repository  WeatherData &io.despicable.chromeos.data.repository  WeatherRepository &io.despicable.chromeos.data.repository  WidgetConfigRepository &io.despicable.chromeos.data.repository  WidgetConfiguration &io.despicable.chromeos.data.repository  WidgetPreferences &io.despicable.chromeos.data.repository  Boolean #io.despicable.chromeos.domain.model  Double #io.despicable.chromeos.domain.model  Float #io.despicable.chromeos.domain.model  FontSize #io.despicable.chromeos.domain.model  GetWeatherDataUseCase #io.despicable.chromeos.domain.model  IconSize #io.despicable.chromeos.domain.model  Int #io.despicable.chromeos.domain.model  List #io.despicable.chromeos.domain.model  Long #io.despicable.chromeos.domain.model  Serializable #io.despicable.chromeos.domain.model  	StateFlow #io.despicable.chromeos.domain.model  String #io.despicable.chromeos.domain.model  UpdateWidgetConfigUseCase #io.despicable.chromeos.domain.model  	ViewModel #io.despicable.chromeos.domain.model  WeatherCondition #io.despicable.chromeos.domain.model  WeatherData #io.despicable.chromeos.domain.model  WidgetConfigUiState #io.despicable.chromeos.domain.model  WidgetConfiguration #io.despicable.chromeos.domain.model  Boolean (io.despicable.chromeos.domain.repository  Flow (io.despicable.chromeos.domain.repository  Int (io.despicable.chromeos.domain.repository  List (io.despicable.chromeos.domain.repository  String (io.despicable.chromeos.domain.repository  WeatherData (io.despicable.chromeos.domain.repository  WeatherRepository (io.despicable.chromeos.domain.repository  WidgetConfigRepository (io.despicable.chromeos.domain.repository  WidgetConfiguration (io.despicable.chromeos.domain.repository  Boolean %io.despicable.chromeos.domain.usecase  Flow %io.despicable.chromeos.domain.usecase  GetWeatherDataUseCase %io.despicable.chromeos.domain.usecase  Inject %io.despicable.chromeos.domain.usecase  Int %io.despicable.chromeos.domain.usecase  List %io.despicable.chromeos.domain.usecase  String %io.despicable.chromeos.domain.usecase  UpdateWidgetConfigUseCase %io.despicable.chromeos.domain.usecase  WeatherData %io.despicable.chromeos.domain.usecase  WeatherRepository %io.despicable.chromeos.domain.usecase  WidgetConfigRepository %io.despicable.chromeos.domain.usecase  WidgetConfiguration %io.despicable.chromeos.domain.usecase  Boolean -io.despicable.chromeos.presentation.ui.config  
Composable -io.despicable.chromeos.presentation.ui.config  Float -io.despicable.chromeos.presentation.ui.config  FontSize -io.despicable.chromeos.presentation.ui.config  IconSize -io.despicable.chromeos.presentation.ui.config  List -io.despicable.chromeos.presentation.ui.config  Modifier -io.despicable.chromeos.presentation.ui.config  OptIn -io.despicable.chromeos.presentation.ui.config  String -io.despicable.chromeos.presentation.ui.config  Unit -io.despicable.chromeos.presentation.ui.config  WeatherData -io.despicable.chromeos.presentation.ui.config  WidgetConfigActivity -io.despicable.chromeos.presentation.ui.config  WidgetConfigViewModel -io.despicable.chromeos.presentation.ui.config  WidgetConfiguration -io.despicable.chromeos.presentation.ui.config  io -io.despicable.chromeos.presentation.ui.config  
despicable 0io.despicable.chromeos.presentation.ui.config.io  chromeos ;io.despicable.chromeos.presentation.ui.config.io.despicable  domain Dio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos  model Kio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain  WeatherData Qio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain.model  WidgetConfiguration Qio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain.model  
Composable -io.despicable.chromeos.presentation.ui.widget  GlanceAppWidget -io.despicable.chromeos.presentation.ui.widget  WeatherData -io.despicable.chromeos.presentation.ui.widget  WidgetConfiguration -io.despicable.chromeos.presentation.ui.widget  Boolean -io.despicable.chromeos.presentation.viewmodel  Float -io.despicable.chromeos.presentation.viewmodel  FontSize -io.despicable.chromeos.presentation.viewmodel  GetWeatherDataUseCase -io.despicable.chromeos.presentation.viewmodel  IconSize -io.despicable.chromeos.presentation.viewmodel  Int -io.despicable.chromeos.presentation.viewmodel  List -io.despicable.chromeos.presentation.viewmodel  	StateFlow -io.despicable.chromeos.presentation.viewmodel  String -io.despicable.chromeos.presentation.viewmodel  UpdateWidgetConfigUseCase -io.despicable.chromeos.presentation.viewmodel  	ViewModel -io.despicable.chromeos.presentation.viewmodel  WeatherData -io.despicable.chromeos.presentation.viewmodel  WidgetConfigUiState -io.despicable.chromeos.presentation.viewmodel  WidgetConfigViewModel -io.despicable.chromeos.presentation.viewmodel  WidgetConfiguration -io.despicable.chromeos.presentation.viewmodel  Boolean io.despicable.chromeos.ui.theme  
ChromeOSTheme io.despicable.chromeos.ui.theme  
Composable io.despicable.chromeos.ui.theme  Unit io.despicable.chromeos.ui.theme  SampleWeatherData io.despicable.chromeos.util  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Any kotlin  Array kotlin  Double kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Long kotlin  OptIn kotlin  String kotlin  Suppress kotlin  Unit kotlin  Int kotlin.Enum  String kotlin.Enum  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  Volatile 
kotlin.jvm  KClass kotlin.reflect  launch kotlinx.coroutines  Boolean kotlinx.coroutines.flow  Float kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  FontSize kotlinx.coroutines.flow  GetWeatherDataUseCase kotlinx.coroutines.flow  IconSize kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  String kotlinx.coroutines.flow  UpdateWidgetConfigUseCase kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  WeatherData kotlinx.coroutines.flow  WidgetConfigUiState kotlinx.coroutines.flow  WidgetConfiguration kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  map kotlinx.coroutines.flow  Serializable kotlinx.serialization  Log android.util  Flow <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  Inject <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  List <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  String <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  
WeatherDao <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  WeatherData <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  Boolean Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Flow Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Inject Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Int Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  List Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  WidgetConfiguration Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  WidgetPreferences Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Boolean Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Float Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  FontSize Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  GetWeatherDataUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  IconSize Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Int Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  List Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  	StateFlow Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  String Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  UpdateWidgetConfigUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WidgetConfigUiState Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WidgetConfiguration Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            