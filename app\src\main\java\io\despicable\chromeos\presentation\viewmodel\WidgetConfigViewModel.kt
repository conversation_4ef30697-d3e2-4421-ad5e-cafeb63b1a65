package io.despicable.chromeos.presentation.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.despicable.chromeos.domain.model.*
import io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase
import io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase
import io.despicable.chromeos.util.SampleWeatherData
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * ViewModel for widget configuration screen with real-time preview
 */
class WidgetConfigViewModel(
    private val widgetId: Int,
    private val getWeatherDataUseCase: GetWeatherDataUseCase,
    private val updateWidgetConfigUseCase: UpdateWidgetConfigUseCase
) : ViewModel() {

    companion object {
        private const val TAG = "WidgetConfigViewModel"
    }

    // UI State
    private val _uiState = MutableStateFlow(WidgetConfigUiState())
    val uiState: StateFlow<WidgetConfigUiState> = _uiState.asStateFlow()

    // Available cities
    private val _availableCities = MutableStateFlow<List<String>>(emptyList())
    val availableCities: StateFlow<List<String>> = _availableCities.asStateFlow()

    // Current weather data for preview
    private val _previewWeatherData = MutableStateFlow<WeatherData?>(null)
    val previewWeatherData: StateFlow<WeatherData?> = _previewWeatherData.asStateFlow()

    init {
        Log.d(TAG, "ViewModel initialized with widgetId: $widgetId")
        loadInitialData()
        observeCitySelection()
    }

    private fun loadInitialData() {
        Log.d(TAG, "loadInitialData() called")
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting data loading...")

                // Load available cities
                Log.d(TAG, "Refreshing weather data...")
                getWeatherDataUseCase.refreshWeatherData()

                // Set available cities immediately
                val cities = SampleWeatherData.getAvailableCities()
                Log.d(TAG, "Available cities: $cities")
                _availableCities.value = cities

                // Load existing widget configuration or create default
                Log.d(TAG, "Loading existing config for widgetId: $widgetId")
                val existingConfig = updateWidgetConfigUseCase.getWidgetConfig(widgetId)
                Log.d(TAG, "Existing config: $existingConfig")

                val defaultCity = cities.firstOrNull() ?: "New York"
                Log.d(TAG, "Default city: $defaultCity")

                val newState = _uiState.value.copy(
                    selectedCity = existingConfig?.cityName ?: defaultCity,
                    selectedIconSize = existingConfig?.iconSize ?: IconSize.MEDIUM,
                    selectedFontSize = existingConfig?.fontSize ?: FontSize.MEDIUM,
                    showHumidity = existingConfig?.showHumidity ?: true,
                    showWindSpeed = existingConfig?.showWindSpeed ?: true,
                    isLoading = false
                )
                Log.d(TAG, "New UI state: $newState")
                _uiState.value = newState

                // Load initial weather data
                Log.d(TAG, "Loading weather data for city: ${_uiState.value.selectedCity}")
                loadWeatherDataForCity(_uiState.value.selectedCity)
            } catch (e: Exception) {
                Log.e(TAG, "Error loading initial data", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    private fun observeCitySelection() {
        Log.d(TAG, "observeCitySelection() called")
        viewModelScope.launch {
            _uiState
                .map { it.selectedCity }
                .distinctUntilChanged()
                .collect { cityName ->
                    Log.d(TAG, "City selection changed to: $cityName")
                    loadWeatherDataForCity(cityName)
                }
        }
    }

    private suspend fun loadWeatherDataForCity(cityName: String) {
        Log.d(TAG, "loadWeatherDataForCity() called with: $cityName")
        try {
            val weatherData = getWeatherDataUseCase.getWeatherByCity(cityName)
                ?: SampleWeatherData.generateRandomWeatherData(cityName)
            Log.d(TAG, "Weather data loaded: $weatherData")
            _previewWeatherData.value = weatherData
        } catch (e: Exception) {
            Log.e(TAG, "Error loading weather data for city: $cityName", e)
            val fallbackData = SampleWeatherData.generateRandomWeatherData(cityName)
            Log.d(TAG, "Using fallback weather data: $fallbackData")
            _previewWeatherData.value = fallbackData
        }
    }

    fun onCitySelected(cityName: String) {
        Log.d(TAG, "onCitySelected() called with: $cityName")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(selectedCity = cityName)
        Log.d(TAG, "City changed from ${oldState.selectedCity} to ${_uiState.value.selectedCity}")
    }

    fun onIconSizeSelected(iconSize: IconSize) {
        Log.d(TAG, "onIconSizeSelected() called with: $iconSize")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(selectedIconSize = iconSize)
        Log.d(TAG, "Icon size changed from ${oldState.selectedIconSize} to ${_uiState.value.selectedIconSize}")
    }

    fun onFontSizeSelected(fontSize: FontSize) {
        Log.d(TAG, "onFontSizeSelected() called with: $fontSize")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(selectedFontSize = fontSize)
        Log.d(TAG, "Font size changed from ${oldState.selectedFontSize} to ${_uiState.value.selectedFontSize}")
    }

    fun onShowHumidityToggled(show: Boolean) {
        Log.d(TAG, "onShowHumidityToggled() called with: $show")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(showHumidity = show)
        Log.d(TAG, "Show humidity changed from ${oldState.showHumidity} to ${_uiState.value.showHumidity}")
    }

    fun onShowWindSpeedToggled(show: Boolean) {
        Log.d(TAG, "onShowWindSpeedToggled() called with: $show")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(showWindSpeed = show)
        Log.d(TAG, "Show wind speed changed from ${oldState.showWindSpeed} to ${_uiState.value.showWindSpeed}")
    }

    fun onIconSizeSliderChanged(value: Float) {
        Log.d(TAG, "onIconSizeSliderChanged() called with value: $value")
        val iconSize = when (value.toInt()) {
            0 -> IconSize.SMALL
            1 -> IconSize.MEDIUM
            2 -> IconSize.LARGE
            3 -> IconSize.EXTRA_LARGE
            else -> IconSize.MEDIUM
        }
        Log.d(TAG, "Mapped slider value $value to icon size: $iconSize")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(selectedIconSize = iconSize)
        Log.d(TAG, "Icon size slider changed from ${oldState.selectedIconSize} to ${_uiState.value.selectedIconSize}")
    }

    fun onFontSizeSliderChanged(value: Float) {
        Log.d(TAG, "onFontSizeSliderChanged() called with value: $value")
        val fontSize = when (value.toInt()) {
            0 -> FontSize.SMALL
            1 -> FontSize.MEDIUM
            2 -> FontSize.LARGE
            3 -> FontSize.EXTRA_LARGE
            else -> FontSize.MEDIUM
        }
        Log.d(TAG, "Mapped slider value $value to font size: $fontSize")
        val oldState = _uiState.value
        _uiState.value = _uiState.value.copy(selectedFontSize = fontSize)
        Log.d(TAG, "Font size slider changed from ${oldState.selectedFontSize} to ${_uiState.value.selectedFontSize}")
    }

    fun getIconSizeSliderValue(): Float {
        val value = when (_uiState.value.selectedIconSize) {
            IconSize.SMALL -> 0f
            IconSize.MEDIUM -> 1f
            IconSize.LARGE -> 2f
            IconSize.EXTRA_LARGE -> 3f
        }
        Log.d(TAG, "getIconSizeSliderValue() returning: $value for ${_uiState.value.selectedIconSize}")
        return value
    }

    fun getFontSizeSliderValue(): Float {
        val value = when (_uiState.value.selectedFontSize) {
            FontSize.SMALL -> 0f
            FontSize.MEDIUM -> 1f
            FontSize.LARGE -> 2f
            FontSize.EXTRA_LARGE -> 3f
        }
        Log.d(TAG, "getFontSizeSliderValue() returning: $value for ${_uiState.value.selectedFontSize}")
        return value
    }

    fun saveConfiguration() {
        Log.d(TAG, "saveConfiguration() called")
        viewModelScope.launch {
            try {
                Log.d(TAG, "Setting loading state to true")
                _uiState.value = _uiState.value.copy(isLoading = true)

                val currentState = _uiState.value
                val config = WidgetConfiguration(
                    widgetId = widgetId,
                    cityName = currentState.selectedCity,
                    iconSize = currentState.selectedIconSize,
                    fontSize = currentState.selectedFontSize,
                    showHumidity = currentState.showHumidity,
                    showWindSpeed = currentState.showWindSpeed
                )
                Log.d(TAG, "Saving configuration: $config")

                updateWidgetConfigUseCase.saveWidgetConfig(config)
                Log.d(TAG, "Configuration saved successfully")

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSaved = true
                )
                Log.d(TAG, "Save state updated - isLoading: false, isSaved: true")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving configuration", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
                Log.d(TAG, "Error state updated - isLoading: false, error: ${e.message}")
            }
        }
    }

    fun getCurrentConfiguration(): WidgetConfiguration {
        val currentState = _uiState.value
        val config = WidgetConfiguration(
            widgetId = widgetId,
            cityName = currentState.selectedCity,
            iconSize = currentState.selectedIconSize,
            fontSize = currentState.selectedFontSize,
            showHumidity = currentState.showHumidity,
            showWindSpeed = currentState.showWindSpeed
        )
        Log.d(TAG, "getCurrentConfiguration() returning: $config")
        return config
    }
}

/**
 * UI State for widget configuration screen
 */
data class WidgetConfigUiState(
    val selectedCity: String = "",
    val selectedIconSize: IconSize = IconSize.MEDIUM,
    val selectedFontSize: FontSize = FontSize.MEDIUM,
    val showHumidity: Boolean = true,
    val showWindSpeed: Boolean = true,
    val isLoading: Boolean = true,
    val isSaved: Boolean = false,
    val error: String? = null
)
