<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Widget Host -->
    <string name="photo_widget_host_description">Decorate your home screen with your favorite photos</string>

    <!-- Home -->
    <string name="photo_widget_home_title">Decorate your home screen with your favorite photos</string>
    <string name="photo_widget_home_instruction">Create a new widget by tapping one of the options. Don\'t worry, you can change this at any time!</string>
    <string name="photo_widget_home_new">New Widget</string>
    <string name="photo_widget_home_current">My Widgets</string>
    <string name="photo_widget_home_settings">Settings</string>
    <string name="photo_widget_home_data_saver">Optimized storage</string>
    <string name="photo_widget_home_appearance">App theme</string>
    <string name="photo_widget_home_true_black_background">True black background</string>
    <string name="photo_widget_home_dynamic_colors">App colors</string>
    <string name="photo_widget_home_share">Share the app</string>
    <string name="photo_widget_home_rate">Write a review</string>
    <string name="photo_widget_home_help">Something is not working?</string>
    <string name="photo_widget_home_common_issues">Common issues</string>
    <string name="photo_widget_home_privacy_policy">Privacy policy</string>
    <string name="photo_widget_home_developer">Developed with ❤ by Filipe Belatti</string>
    <string name="photo_widget_home_version">App version: %s</string>
    <string name="photo_widget_home_view_licenses">OSS Licenses</string>

    <plurals name="photo_widget_home_share_received">
        <item quantity="one">1 photo received. Create a new widget or choose an existing one to add it.</item>
        <item quantity="other">%d photos received. Create a new widget or choose an existing one to add them.</item>
    </plurals>

    <string name="photo_widget_home_pinning_not_supported_title">Add a widget directly from your home screen</string>
    <string name="photo_widget_home_pinning_not_supported_message">1. Tap and hold an empty space\n\n2. Choose the widgets menu\n\n3. Search for Material Photo Widget\n\n4. Configure and save!</string>

    <!-- My Widgets -->
    <string name="photo_widget_home_empty_widgets">You haven\'t added any widgets yet.\nWidgets added to your home screen will appear here.</string>
    <string name="photo_widget_home_filter_all">All</string>
    <string name="photo_widget_home_filter_photos">Photos</string>
    <string name="photo_widget_home_filter_folder">Folder</string>

    <string name="photo_widget_home_locked_label">Locked</string>
    <string name="photo_widget_home_removed_label">Removed</string>
    <string name="photo_widget_home_removed_widgets_hint">Removed widgets are no longer on your home screen. They are permanently deleted from the app after 7 days. You can choose to "Keep" them.</string>

    <string name="photo_widget_home_my_widget_action_sync">Sync with folder photos</string>
    <string name="photo_widget_home_my_widget_action_edit">View / Edit</string>
    <string name="photo_widget_home_my_widget_action_duplicate">Duplicate</string>
    <string name="photo_widget_home_my_widget_action_lock">Lock with current photo</string>
    <string name="photo_widget_home_my_widget_action_unlock">Unlock widget</string>
    <string name="photo_widget_home_my_widget_lock_explainer">Photos will not change automatically or on tap when the widget is locked</string>
    <string name="photo_widget_home_removed_widget_action_restore">Restore</string>
    <string name="photo_widget_home_removed_widget_action_keep">Keep</string>
    <string name="photo_widget_home_removed_widget_action_delete">Delete permanently</string>

    <string name="photo_widget_home_my_widget_syncing_feedback">Fetching latest photos from folder…</string>

    <!-- Preferences -->
    <string name="preferences_data_saver">Reduce storage usage</string>
    <string name="preferences_data_saver_description">When enabled, photos added to widgets are compressed to save storage. This usually won\'t affect quality, but if your photos contain small text, it\'s recommended to turn this off.\n\nThis setting only affects photos added after it is changed.\n\nIt does not apply to synced folders, as those photos are read directly from your device storage.</string>

    <string name="preferences_appearance_follow_system">Follow system</string>
    <string name="preferences_appearance_light">Light</string>
    <string name="preferences_appearance_dark">Dark</string>

    <string name="preferences_dynamic_colors_enabled">Dynamic colors</string>
    <string name="preferences_dynamic_colors_disabled">Default colors</string>

    <!-- Widget Defaults -->
    <string name="widget_defaults_title">Widget defaults</string>
    <string name="widget_defaults_explanation">These values are pre-loaded when you create a new widget. They are useful for setting up multiple widgets with the same configuration, eliminating the need to adjust settings each time. Note that any changes to these values won’t affect existing widgets.</string>
    <string name="widget_defaults_source">Source</string>
    <string name="widget_defaults_shuffle">Shuffle</string>
    <string name="widget_defaults_cycling">Photo cycling</string>
    <string name="widget_defaults_shape">Shape</string>
    <string name="widget_defaults_corner_radius">Roundness</string>
    <string name="widget_defaults_opacity">Opacity</string>
    <string name="widget_defaults_saturation">Saturation</string>
    <string name="widget_defaults_brightness">Brightness</string>
    <string name="widget_defaults_tap_action">Tap action</string>
    <string name="widget_defaults_reset">Reset values</string>

    <!-- Share -->
    <string name="share_title">Spread the word</string>
    <string name="share_text">Add your favorite photos to your home screen with Material Photo Widget. Download for free at %s</string>

    <!-- Source -->
    <string name="photo_widget_source_photos">Photos</string>
    <string name="photo_widget_source_directory">Folder</string>

    <!-- Aspect Ratio -->
    <string name="photo_widget_aspect_ratio_title">Format</string>
    <string name="photo_widget_aspect_ratio_shape">Shapes</string>
    <string name="photo_widget_aspect_ratio_shape_description">Material You shapes</string>
    <string name="photo_widget_aspect_ratio_square">Square</string>
    <string name="photo_widget_aspect_ratio_square_description" translatable="false">1:1</string>
    <string name="photo_widget_aspect_ratio_tall">Tall</string>
    <string name="photo_widget_aspect_ratio_tall_description" translatable="false">10:16</string>
    <string name="photo_widget_aspect_ratio_wide">Wide</string>
    <string name="photo_widget_aspect_ratio_wide_description" translatable="false">16:10</string>
    <string name="photo_widget_aspect_ratio_original">Original</string>
    <string name="photo_widget_aspect_ratio_original_description">Match each photo</string>
    <string name="photo_widget_aspect_ratio_fill_widget">Fill</string>
    <string name="photo_widget_aspect_ratio_fill_widget_description">Match the widget size</string>

    <!-- Configure -->
    <string name="photo_widget_configure_navigate_back_warning">Any changes will be lost, go back anyway?</string>

    <string name="photo_widget_configure_tab_content">Content</string>
    <string name="photo_widget_configure_tab_appearance">Appearance</string>
    <string name="photo_widget_configure_tab_behavior">Behavior</string>

    <string name="photo_widget_configure_import_prompt">Import from another widget?</string>
    <string name="photo_widget_configure_import_prompt_action">Choose</string>
    <string name="photo_widget_configure_import_dialog_title">Choose widget</string>
    <string name="photo_widget_configure_import_dialog_description">All photos and configurations will be copied to the new widget</string>

    <string name="photo_widget_configure_import_error">Unable to import all selected photos</string>
    <string name="photo_widget_configure_too_many_photos_error">Each synced folder can contain a maximum of 3000 photos. You can select more than one, and each subfolder is counted separately.\n\nYou cannot select your camera roll. Photos are processed one by one, and folders with many thousands result in severe performance issues in the widget editor, as well as on the home screen when switching photos.</string>
    <string name="photo_widget_configure_missing_photos_error">The widget must have at least one photo. Widgets can only be deleted directly from the home screen.</string>

    <string name="photo_widget_configure_menu_source">Source</string>
    <string name="photo_widget_configure_source_description">You can choose each photo individually or sync this widget with one or more folders. When syncing, all photos in the folder are automatically added to the widget. The content is updated every 24 hours.</string>
    <string name="photo_widget_configure_source_selection_photos">Only the photos you selected will be displayed in this widget.</string>
    <string name="photo_widget_configure_source_selection_directory_empty">This widget is not synced with any folder yet.</string>
    <string name="photo_widget_configure_source_selection_directory_non_empty">This widget is synced with these folders:</string>
    <string name="photo_widget_configure_source_keep_current">Keep current source</string>
    <string name="photo_widget_configure_source_set_directory">Set source to Folder</string>
    <string name="photo_widget_configure_source_set_photos">Set source to Photos</string>
    <string name="photo_widget_configure_source_warning">Changing the source will replace the current photos from this widget</string>

    <string name="photo_widget_configure_menu_crop">Crop</string>
    <string name="photo_widget_configure_menu_remove">Remove</string>
    <string name="photo_widget_configure_menu_move_left">Move left</string>
    <string name="photo_widget_configure_menu_move_right">Move right</string>

    <string name="photo_widget_configure_photos_pending_deletion">Recently deleted photos</string>
    <string name="photo_widget_configure_photos_excluded">Excluded from widget</string>

    <string name="photo_widget_configure_pick_photo">Pick photos</string>
    <string name="photo_widget_configure_pick_folder">Pick folder</string>
    <string name="photo_widget_configure_change_source">Change source</string>

    <string name="photo_widget_configure_select_cycling_mode">Photo cycling</string>
    <string name="photo_widget_configure_cycling_mode_interval">Interval</string>
    <string name="photo_widget_configure_cycling_mode_schedule">Schedule</string>
    <string name="photo_widget_configure_cycling_mode_disabled">Disabled</string>

    <string name="photo_widget_configure_cycling_mode_interval_description">The widget will switch automatically to the next photo at the following interval:</string>
    <string name="photo_widget_configure_cycling_mode_schedule_description">The widget will switch automatically to the next photo every day at the following scheduled times:</string>
    <string name="photo_widget_configure_cycling_mode_disabled_description">The widget won\'t cycle through photos automatically. Tap the widget edges to switch to the next photo.</string>

    <string name="photo_widget_configure_interval_current_label">Every %s</string>
    <plurals name="photo_widget_configure_interval_current_seconds">
        <item quantity="one">%d second</item>
        <item quantity="other">%d seconds</item>
    </plurals>
    <plurals name="photo_widget_configure_interval_current_minutes">
        <item quantity="one">%d minute</item>
        <item quantity="other">%d minutes</item>
    </plurals>
    <plurals name="photo_widget_configure_interval_current_hours">
        <item quantity="one">%d hour</item>
        <item quantity="other">%d hours</item>
    </plurals>
    <plurals name="photo_widget_configure_interval_current_days">
        <item quantity="one">%d day</item>
        <item quantity="other">%d days</item>
    </plurals>
    <string name="photo_widget_configure_interval_seconds_label">Seconds</string>
    <string name="photo_widget_configure_interval_minutes_label">Minutes</string>
    <string name="photo_widget_configure_interval_hours_label">Hours</string>
    <string name="photo_widget_configure_interval_days_label">Days</string>

    <string name="photo_widget_configure_schedule_placeholder">Schedule up to 4 times to switch your photos automatically.</string>
    <string name="photo_widget_configure_schedule_add_new">Add new</string>
    <string name="photo_widget_configure_schedule_limit_reached">Maximum of 4 times reached</string>
    <plurals name="photo_widget_configure_schedule_times">
        <item quantity="one">1 time selected</item>
        <item quantity="other">%d times selected</item>
    </plurals>

    <string name="photo_widget_configure_interval_warning">The actual interval or scheduled time can vary due to restrictions imposed by Android to optimize battery life. To ensure it works as desired, grant the permission below</string>

    <string name="photo_widget_configure_interval_grant_permission">Grant exact alarm permission</string>
    <string name="photo_widget_configure_interval_permission_dialog_title">About this permission</string>
    <string name="photo_widget_configure_interval_permission_dialog_description">Android can restrict background work to optimize battery life, resulting in longer intervals between each photo.\n\nTo update widgets precisely at the selected interval, open settings and allow setting alarms and reminders.</string>
    <string name="photo_widget_configure_interval_open_settings">Open settings</string>

    <string name="photo_widget_configure_border">Border</string>
    <string name="photo_widget_configure_border_none">None</string>
    <string name="photo_widget_configure_border_dynamic">Dynamic</string>
    <string name="photo_widget_configure_border_color">Colored</string>
    <string name="photo_widget_configure_border_color_palette">Palette</string>
    <string name="photo_widget_configure_border_color_palette_dominant">Dominant color</string>
    <string name="photo_widget_configure_border_color_palette_vibrant">Vibrant color</string>
    <string name="photo_widget_configure_border_color_palette_muted">Muted color</string>

    <string name="photo_widget_configure_border_explanation">Changes will take place during the next widget update after you change your phone\'s theme color. To update all widgets immediately, simply open the app.</string>

    <string name="photo_widget_configure_offset">Offset</string>
    <string name="photo_widget_configure_offset_current_values">Horizontal: %1$d, Vertical: %2$d</string>
    <string name="photo_widget_configure_offset_current_horizontal">Horizontal: %1$d</string>
    <string name="photo_widget_configure_offset_current_vertical">Vertical: %1$d</string>

    <string name="photo_widget_configure_padding">Padding</string>

    <string name="photo_widget_configure_tap_action">Tap action</string>
    <string name="photo_widget_configure_tap_action_description">1x1 widgets can have a single tap action. This is applied to the middle area of larger widgets (blinking green), and you can also tap the sides to switch photos.</string>
    <string name="photo_widget_configure_tap_action_none">None</string>
    <string name="photo_widget_configure_tap_action_view_full_screen">View photo in full screen</string>
    <string name="photo_widget_configure_tap_action_view_in_gallery">View with a gallery app</string>
    <string name="photo_widget_configure_tap_action_view_next_photo">View next photo</string>
    <string name="photo_widget_configure_tap_action_choose_next_photo">Choose next photo</string>
    <string name="photo_widget_configure_tap_action_app_shortcut">Open an app</string>
    <string name="photo_widget_configure_tap_action_url_shortcut">Open a link</string>
    <string name="photo_widget_configure_tap_action_toggle_cycling">Enable/disable cycle</string>
    <string name="photo_widget_configure_tap_action_toggle_cycling_description">Tap to pause the automatic photo cycling. Tap again to resume. This action has no effect if the widget is locked.</string>

    <string name="photo_widget_configure_tap_action_increase_brightness">Automatically increase brightness</string>
    <string name="photo_widget_configure_tap_action_view_original_photo">View original photo (no crop or rotation)</string>
    <string name="photo_widget_configure_tap_action_do_not_shuffle">Do not shuffle when using the viewer buttons</string>
    <string name="photo_widget_configure_tap_action_keep_current_photo">Do not change the current photo when using the viewer buttons</string>
    <string name="photo_widget_configure_tap_action_disable_side_actions">Disable tapping on the edges on the home screen</string>
    <string name="photo_widget_configure_tap_action_gallery_description">Available for folder-based widgets. Photo-based widgets will use \"View photo in full screen\" instead. You may also choose which gallery app to use.</string>
    <string name="photo_widget_configure_tap_action_choose_app">Choose app</string>
    <string name="photo_widget_configure_tap_action_disable_tap">Disable tapping on the edges while cycling is paused</string>

    <string name="photo_widget_configure_add_to_home">Add to home screen</string>
    <string name="photo_widget_configure_save_changes">Save changes</string>

    <string name="photo_widget_configure_widget_pinned">Widget added! Check your home screen…</string>

    <string name="photo_widget_time_picker_title">Select time</string>

    <!-- Help -->
    <string name="help_article_title_1">Adding a widget to the home screen is not working</string>
    <string name="help_article_body_1">If you have configured a widget and nothing happens when you try to add it to the home screen, or if it\'s added but doesn\'t display the selected photos, you can try adding it directly from your home screen.\n\nInstructions may vary depending on your Android version, device manufacturer, and home screen launcher, but generally, the process is as follows:\n\n1. Long press on an empty space on your home screen until a pop-up menu appears.\n2. Locate the "Widgets" option.\n3. Search for "Material Photo Widget" within the available widgets.\n4. Drag it to your home screen and configure it as usual.</string>

    <string name="help_article_title_2">Photos are not changing automatically</string>
    <string name="help_article_body_2">The selected interval or scheduled times are an approximation of when the next photo will be displayed. Android considers several factors when determining the actual time, and each device manufacturer may impose restrictions on background processes.\n\nTo ensure that the photos change exactly when you want them to, grant the app "Exact Alarms" permission and make sure no battery restrictions are applied to it in your phone\'s settings.</string>

    <string name="help_article_title_3">The widget does not work after restarting the phone</string>
    <string name="help_article_body_3">When your phone restarts, Android sends a signal to apps to notify them, so they can restart themselves. Unfortunately, some device manufacturers (such as Xiaomi, Redmi, Huawei, and others) block this signal.\n\nTo enable this functionality, change the "Auto Start" permission in your security app.</string>

    <string name="help_article_title_4">The widget size is too small</string>
    <string name="help_article_body_4">After adding the widget to your home screen, you can long press it to reveal the resizing option. Then, drag its borders to adjust it to your desired size.</string>

    <string name="restriction_warning_hint">Background activity is restricted. Widgets may not work correctly. Tap to learn more.</string>
    <string name="restriction_warning_dialog_title">Background work restrictions and battery optimization</string>
    <string name="restriction_warning_dialog_body_1">Material Photo Widget needs to run in the background to update your widgets. It only runs when needed, but if battery usage is set to "optimized" Android may stop the app, causing widgets to stop working correctly.</string>
    <string name="restriction_warning_dialog_body_2">Open app settings and change the battery usage to Unrestricted. If this option isn\'t available, check the phone\'s power settings.</string>
    <string name="restriction_warning_dialog_body_3">Some phones are even more restrictive and may block the app from starting automatically after a restart. Settings vary by device — visit https://dontkillmyapp.com for specific instructions.</string>
    <string name="restriction_warning_open_app_settings">Open app settings</string>
    <string name="restriction_warning_open_power_settings">Open power settings</string>

    <!-- Viewer -->
    <string name="photo_widget_viewer_actions_pinch">Pinch to zoom</string>
    <string name="photo_widget_viewer_actions_drag">Drag to move</string>
    <string name="photo_widget_viewer_actions_tap">Tap to dismiss</string>

    <!-- Cycling feedback -->
    <string name="photo_widget_cycling_feedback_paused">Cycle paused</string>
    <string name="photo_widget_cycling_feedback_resumed">Cycle resumed</string>

    <!-- Common Actions -->
    <string name="photo_widget_action_yes">Yes</string>
    <string name="photo_widget_action_no">No</string>
    <string name="photo_widget_action_continue">Continue</string>
    <string name="photo_widget_action_cancel">Cancel</string>
    <string name="photo_widget_action_apply">Apply</string>
    <string name="photo_widget_action_confirm">Confirm</string>
    <string name="photo_widget_action_got_it">Got it</string>

    <!-- Content Description -->
    <string name="photo_widget_cd_host">Material Photo home screen widget</string>
</resources>
