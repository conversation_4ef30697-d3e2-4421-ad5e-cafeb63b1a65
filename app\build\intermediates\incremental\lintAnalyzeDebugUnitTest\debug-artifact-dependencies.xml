<dependencies>
  <compile
      roots=":@@:app::debug,io.insert-koin:koin-android:4.0.4@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.hilt:hilt-navigation-compose:1.2.0@aar,androidx.hilt:hilt-navigation:1.2.0@aar,com.google.dagger:hilt-android:2.49@aar,androidx.fragment:fragment:1.8.5@aar,androidx.fragment:fragment-ktx:1.8.5@aar,androidx.activity:activity:1.10.1@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-compose-android:2.9.0@aar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,io.insert-koin:koin-compose-viewmodel-jvm:4.0.4@jar,io.insert-koin:koin-core-viewmodel-jvm:4.0.4@jar,org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03@aar,androidx.work:work-runtime-ktx:2.10.1@aar,androidx.work:work-runtime:2.10.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.glance:glance:1.1.1@aar,androidx.glance:glance-material3:1.1.1@aar,androidx.glance:glance-appwidget:1.1.1@aar,androidx.compose.material3:material3-android:1.3.2@aar,androidx.compose.ui:ui-util-android:1.8.1@aar,androidx.compose.ui:ui-text-android:1.8.1@aar,androidx.compose.foundation:foundation-layout-android:1.8.1@aar,androidx.compose.material:material-ripple-android:1.8.1@aar,androidx.compose.foundation:foundation-android:1.8.1@aar,androidx.compose.animation:animation-core-android:1.8.1@aar,androidx.compose.animation:animation-android:1.8.1@aar,androidx.compose.ui:ui-geometry-android:1.8.1@aar,androidx.compose.ui:ui-tooling-data-android:1.8.1@aar,androidx.compose.ui:ui-unit-android:1.8.1@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.1@aar,androidx.compose.ui:ui-graphics-android:1.8.1@aar,androidx.compose.ui:ui-tooling-android:1.8.1@aar,androidx.compose.material:material-icons-extended-android:1.7.8@aar,androidx.compose.material:material-icons-core-android:1.7.8@aar,androidx.compose.ui:ui-android:1.8.1@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-common-java8:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.room:room-common-jvm:2.7.1@jar,androidx.room:room-runtime-android:2.7.1@aar,androidx.room:room-ktx:2.7.1@aar,io.insert-koin:koin-compose-jvm:4.0.4@jar,androidx.compose.runtime:runtime-saveable-android:1.8.1@aar,androidx.compose.runtime:runtime-android:1.8.1@aar,androidx.datastore:datastore-android:1.1.6@aar,androidx.datastore:datastore-preferences-android:1.1.6@aar,androidx.datastore:datastore-core-okio-jvm:1.1.6@jar,androidx.datastore:datastore-preferences-core-android:1.1.6@aar,androidx.datastore:datastore-core-android:1.1.6@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1@jar,com.mikepenz:aboutlibraries-compose-android:12.0.1@aar,com.mikepenz:aboutlibraries-compose-core-android:12.0.1@aar,com.mikepenz:aboutlibraries-core-android:12.0.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-ktx:1.5.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.sqlite:sqlite-framework-android:2.5.0@aar,androidx.sqlite:sqlite-android:2.5.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,io.insert-koin:koin-core-jvm:4.0.4@jar,sh.calvin.reorderable:reorderable-android-debug:2.4.3@aar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar,org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar,com.google.dagger:hilt-core:2.49@jar,com.google.dagger:dagger:2.49@jar,javax.inject:javax.inject:1@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,org.jspecify:jspecify:1.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,com.google.dagger:dagger-lint-aar:2.49@aar,com.google.code.findbugs:jsr305:3.0.2@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="io.insert-koin:koin-android:4.0.4@aar"
        simpleName="io.insert-koin:koin-android"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.2.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.2.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="com.google.dagger:hilt-android:2.49@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.8.5@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.5@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-compose-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-compose-android"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="io.insert-koin:koin-compose-viewmodel-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-compose-viewmodel-jvm"/>
    <dependency
        name="io.insert-koin:koin-core-viewmodel-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-core-viewmodel-jvm"/>
    <dependency
        name="org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03@aar"
        simpleName="org.jetbrains.androidx.core:core-bundle-android-debug"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.10.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.glance:glance:1.1.1@aar"
        simpleName="androidx.glance:glance"/>
    <dependency
        name="androidx.glance:glance-material3:1.1.1@aar"
        simpleName="androidx.glance:glance-material3"/>
    <dependency
        name="androidx.glance:glance-appwidget:1.1.1@aar"
        simpleName="androidx.glance:glance-appwidget"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.2@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.1@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.8.1@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.1@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.1@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.1@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common-jvm:2.7.1@jar"
        simpleName="androidx.room:room-common-jvm"/>
    <dependency
        name="androidx.room:room-runtime-android:2.7.1@aar"
        simpleName="androidx.room:room-runtime-android"/>
    <dependency
        name="androidx.room:room-ktx:2.7.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="io.insert-koin:koin-compose-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-compose-jvm"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.1@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.1@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.6@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-preferences-core-android"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-datetime-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="com.mikepenz:aboutlibraries-compose-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-compose-android"/>
    <dependency
        name="com.mikepenz:aboutlibraries-compose-core-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-compose-core-android"/>
    <dependency
        name="com.mikepenz:aboutlibraries-core-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-core-android"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-ktx:1.5.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-framework-android"/>
    <dependency
        name="androidx.sqlite:sqlite-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-android"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="sh.calvin.reorderable:reorderable-android-debug:2.4.3@aar"
        simpleName="sh.calvin.reorderable:reorderable-android-debug"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03@aar"
        simpleName="org.jetbrains.androidx.core:core-uri-android-debug"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.dagger:hilt-core:2.49@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.49@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.49@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
  </compile>
  <package
      roots="io.insert-koin:koin-android:4.0.4@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.hilt:hilt-navigation-compose:1.2.0@aar,androidx.hilt:hilt-navigation:1.2.0@aar,com.google.dagger:hilt-android:2.49@aar,androidx.fragment:fragment:1.8.5@aar,androidx.fragment:fragment-ktx:1.8.5@aar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-compose-android:2.9.0@aar,androidx.activity:activity:1.10.1@aar,androidx.glance:glance-appwidget-external-protobuf:1.1.1@jar,androidx.glance:glance-appwidget-proto:1.1.1@jar,androidx.glance:glance:1.1.1@aar,androidx.glance:glance-appwidget:1.1.1@aar,androidx.glance:glance-material3:1.1.1@aar,androidx.compose.material3:material3-android:1.3.2@aar,com.mikepenz:aboutlibraries-compose-android:12.0.1@aar,sh.calvin.reorderable:reorderable-android-debug:2.4.3@aar,com.mikepenz:aboutlibraries-compose-core-android:12.0.1@aar,io.insert-koin:koin-compose-viewmodel-jvm:4.0.4@jar,androidx.compose.material:material-android:1.8.1@aar,androidx.compose.foundation:foundation-layout-android:1.8.1@aar,androidx.compose.material:material-ripple-android:1.8.1@aar,androidx.compose.foundation:foundation-android:1.8.1@aar,androidx.compose.animation:animation-android:1.8.1@aar,androidx.compose.animation:animation-core-android:1.8.1@aar,org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03@aar,androidx.compose.ui:ui-tooling-data-android:1.8.1@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.1@aar,androidx.compose.ui:ui-geometry-android:1.8.1@aar,androidx.compose.ui:ui-util-android:1.8.1@aar,androidx.compose.ui:ui-text-android:1.8.1@aar,androidx.compose.ui:ui-unit-android:1.8.1@aar,androidx.compose.ui:ui-tooling-android:1.8.1@aar,androidx.compose.ui:ui-graphics-android:1.8.1@aar,androidx.work:work-runtime-ktx:2.10.1@aar,androidx.work:work-runtime:2.10.1@aar,androidx.core:core-remoteviews:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.emoji2:emoji2-views-helper:1.4.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,io.insert-koin:koin-core-viewmodel-jvm:4.0.4@jar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-compose-android:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-common-java8:2.9.0@jar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar,androidx.compose.material:material-icons-extended-android:1.7.8@aar,androidx.compose.material:material-icons-core-android:1.7.8@aar,androidx.compose.ui:ui-android:1.8.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.room:room-common-jvm:2.7.1@jar,androidx.room:room-runtime-android:2.7.1@aar,androidx.room:room-ktx:2.7.1@aar,io.insert-koin:koin-compose-jvm:4.0.4@jar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.6@jar,androidx.datastore:datastore-core-okio-jvm:1.1.6@jar,androidx.datastore:datastore-preferences-android:1.1.6@aar,androidx.datastore:datastore-android:1.1.6@aar,androidx.datastore:datastore-preferences-proto:1.1.6@jar,androidx.datastore:datastore-preferences-core-android:1.1.6@aar,androidx.datastore:datastore-core-android:1.1.6@aar,androidx.compose.runtime:runtime-saveable-android:1.8.1@aar,androidx.compose.runtime:runtime-android:1.8.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2@jar,com.mikepenz:aboutlibraries-core-android:12.0.1@aar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,io.insert-koin:koin-core-jvm:4.0.4@jar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar,androidx.sqlite:sqlite-framework-android:2.5.0@aar,androidx.sqlite:sqlite-android:2.5.0@aar,org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.collection:collection-ktx:1.5.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar,co.touchlab:stately-concurrent-collections-jvm:2.1.0@jar,co.touchlab:stately-concurrency-jvm:2.1.0@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlinx:atomicfu-jvm:0.23.2@jar,androidx.performance:performance-annotation-android:1.0.0-alpha01@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,co.touchlab:stately-strict-jvm:2.1.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar,com.google.dagger:hilt-core:2.49@jar,com.google.dagger:dagger:2.49@jar,javax.inject:javax.inject:1@jar,org.jetbrains:annotations:23.0.0@jar,org.jspecify:jspecify:1.0.0@jar,com.google.guava:listenablefuture:1.0@jar,com.google.dagger:dagger-lint-aar:2.49@aar,com.google.code.findbugs:jsr305:3.0.2@jar">
    <dependency
        name="io.insert-koin:koin-android:4.0.4@aar"
        simpleName="io.insert-koin:koin-android"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.2.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.2.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="com.google.dagger:hilt-android:2.49@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.8.5@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.5@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-compose-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-compose-android"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.glance:glance-appwidget-external-protobuf:1.1.1@jar"
        simpleName="androidx.glance:glance-appwidget-external-protobuf"/>
    <dependency
        name="androidx.glance:glance-appwidget-proto:1.1.1@jar"
        simpleName="androidx.glance:glance-appwidget-proto"/>
    <dependency
        name="androidx.glance:glance:1.1.1@aar"
        simpleName="androidx.glance:glance"/>
    <dependency
        name="androidx.glance:glance-appwidget:1.1.1@aar"
        simpleName="androidx.glance:glance-appwidget"/>
    <dependency
        name="androidx.glance:glance-material3:1.1.1@aar"
        simpleName="androidx.glance:glance-material3"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.2@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="com.mikepenz:aboutlibraries-compose-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-compose-android"/>
    <dependency
        name="sh.calvin.reorderable:reorderable-android-debug:2.4.3@aar"
        simpleName="sh.calvin.reorderable:reorderable-android-debug"/>
    <dependency
        name="com.mikepenz:aboutlibraries-compose-core-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-compose-core-android"/>
    <dependency
        name="io.insert-koin:koin-compose-viewmodel-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-compose-viewmodel-jvm"/>
    <dependency
        name="androidx.compose.material:material-android:1.8.1@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.1@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.8.1@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.1@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.1@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.1@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03@aar"
        simpleName="org.jetbrains.compose.ui:ui-backhandler-android-debug"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.10.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.10.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.core:core-remoteviews:1.1.0@aar"
        simpleName="androidx.core:core-remoteviews"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="io.insert-koin:koin-core-viewmodel-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-core-viewmodel-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-compose-android"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.1@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03@aar"
        simpleName="org.jetbrains.androidx.core:core-bundle-android-debug"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common-jvm:2.7.1@jar"
        simpleName="androidx.room:room-common-jvm"/>
    <dependency
        name="androidx.room:room-runtime-android:2.7.1@aar"
        simpleName="androidx.room:room-runtime-android"/>
    <dependency
        name="androidx.room:room-ktx:2.7.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="io.insert-koin:koin-compose-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-compose-jvm"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.6@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.6@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.6@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-preferences-core-android"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.6@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.1@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.1@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-datetime-jvm"/>
    <dependency
        name="com.mikepenz:aboutlibraries-core-android:12.0.1@aar"
        simpleName="com.mikepenz:aboutlibraries-core-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:4.0.4@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-framework-android"/>
    <dependency
        name="androidx.sqlite:sqlite-android:2.5.0@aar"
        simpleName="androidx.sqlite:sqlite-android"/>
    <dependency
        name="org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03@aar"
        simpleName="org.jetbrains.androidx.core:core-uri-android-debug"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.collection:collection-ktx:1.5.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.1.0@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.1.0@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlinx:atomicfu-jvm:0.23.2@jar"
        simpleName="org.jetbrains.kotlinx:atomicfu-jvm"/>
    <dependency
        name="androidx.performance:performance-annotation-android:1.0.0-alpha01@aar"
        simpleName="androidx.performance:performance-annotation-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="co.touchlab:stately-strict-jvm:2.1.0@jar"
        simpleName="co.touchlab:stately-strict-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.dagger:hilt-core:2.49@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.49@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.49@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
  </package>
</dependencies>
