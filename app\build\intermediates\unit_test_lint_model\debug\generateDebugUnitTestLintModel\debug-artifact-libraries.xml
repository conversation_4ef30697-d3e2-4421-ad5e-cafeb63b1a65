<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="io.insert-koin:koin-android:4.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4\jars\classes.jar"
      resolved="io.insert-koin:koin-android:4.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f82603240625ff099e2ad848ad86436b\transformed\koin-android-4.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39208631b02ee562008e36749252cf44\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39208631b02ee562008e36749252cf44\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5a5ac5787a9ad7f21a44018aa6dab8\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5a5ac5787a9ad7f21a44018aa6dab8\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation-compose:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3832d2c8f326ec4187ac4a1d32501c91\transformed\hilt-navigation-compose-1.2.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation-compose:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3832d2c8f326ec4187ac4a1d32501c91\transformed\hilt-navigation-compose-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4b440f75078133df6ad6510cdc5bb4\transformed\hilt-navigation-1.2.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c4b440f75078133df6ad6510cdc5bb4\transformed\hilt-navigation-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android:2.49@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47301a4e8af2ebaaa3063e6479fbc928\transformed\hilt-android-2.49\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.49"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47301a4e8af2ebaaa3063e6479fbc928\transformed\hilt-android-2.49"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.8.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1545388fb70c41300643a5971266c0b\transformed\fragment-1.8.5\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.8.5"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1545388fb70c41300643a5971266c0b\transformed\fragment-1.8.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.8.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7e1a03de3e844a22441a37d5a0c3a8\transformed\fragment-ktx-1.8.5\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.8.5"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7e1a03de3e844a22441a37d5a0c3a8\transformed\fragment-ktx-1.8.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58cf529ee9bcb6985359e9d96cd95a3a\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58cf529ee9bcb6985359e9d96cd95a3a\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73471e8268cfdfdc3680c52ecc7020\transformed\navigation-common-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73471e8268cfdfdc3680c52ecc7020\transformed\navigation-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc9cb04f6aac2669ae6fa2db8935d260\transformed\navigation-compose-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc9cb04f6aac2669ae6fa2db8935d260\transformed\navigation-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e4ab69e1da09d111257f580b63f8e31\transformed\navigation-runtime-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e4ab69e1da09d111257f580b63f8e31\transformed\navigation-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e916fe6b5dc393a134112db8a47cfd2\transformed\activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e916fe6b5dc393a134112db8a47cfd2\transformed\activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10fa6fd426766e10ee7854512001ba3d\transformed\activity-compose-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10fa6fd426766e10ee7854512001ba3d\transformed\activity-compose-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.insert-koin:koin-compose-viewmodel-jvm:4.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-compose-viewmodel-jvm\4.0.4\3d3d6b5f8a9b486d12f294f2aee94755404d071b\koin-compose-viewmodel-jvm-4.0.4.jar"
      resolved="io.insert-koin:koin-compose-viewmodel-jvm:4.0.4"/>
  <library
      name="io.insert-koin:koin-core-viewmodel-jvm:4.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-viewmodel-jvm\4.0.4\13a8ef50ed8a827de227d1526f7b201aeb02254c\koin-core-viewmodel-jvm-4.0.4.jar"
      resolved="io.insert-koin:koin-core-viewmodel-jvm:4.0.4"/>
  <library
      name="org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49cdc21a8cd3e0df61b3ff37e4cf8ea4\transformed\core-bundle-debug\jars\classes.jar"
      resolved="org.jetbrains.androidx.core:core-bundle-android-debug:1.1.0-alpha03"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49cdc21a8cd3e0df61b3ff37e4cf8ea4\transformed\core-bundle-debug"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecc1c2981ed32ff04a344f0f0ac80de2\transformed\work-runtime-ktx-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ecc1c2981ed32ff04a344f0f0ac80de2\transformed\work-runtime-ktx-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df402849ddc0f9e7f1a0619c5d8094da\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df402849ddc0f9e7f1a0619c5d8094da\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e8aca9b8f5a0b62b3b875c0d203bd1\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e8aca9b8f5a0b62b3b875c0d203bd1\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7459399df2d5d90f083c2a80e08ee\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7459399df2d5d90f083c2a80e08ee\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25663c94ddc73789937d3a1ee64b0b07\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25663c94ddc73789937d3a1ee64b0b07\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aabc9fb396aa0e7b8f5b63a834da2bdd\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aabc9fb396aa0e7b8f5b63a834da2bdd\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1574cd25f0613d1e447c947dbcccd8aa\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1574cd25f0613d1e447c947dbcccd8aa\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.glance:glance:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39babb803641efb4abb931d2a7a5d339\transformed\glance-1.1.1\jars\classes.jar"
      resolved="androidx.glance:glance:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39babb803641efb4abb931d2a7a5d339\transformed\glance-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.glance:glance-material3:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b6b566591c56e227453dc103f893054\transformed\glance-material3-1.1.1\jars\classes.jar"
      resolved="androidx.glance:glance-material3:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b6b566591c56e227453dc103f893054\transformed\glance-material3-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.glance:glance-appwidget:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\jars\classes.jar"
      resolved="androidx.glance:glance-appwidget:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c91fc286a144f7dd3fed97bfa6a44d86\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c91fc286a144f7dd3fed97bfa6a44d86\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e745d6608c68c15a01529d144dc6b8c\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e745d6608c68c15a01529d144dc6b8c\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e3cc776ddb8090a4936e666954523f6\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e3cc776ddb8090a4936e666954523f6\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa607cf7e6bb1da5c7d21ce61c034c9c\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa607cf7e6bb1da5c7d21ce61c034c9c\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2431c590a4af45b7a501b8b3a1c4a590\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2431c590a4af45b7a501b8b3a1c4a590\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e1d3faf204d8dc115e2ef9096d86cb\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e1d3faf204d8dc115e2ef9096d86cb\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d6f01189cd3d96670bca273ce5a2082\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d6f01189cd3d96670bca273ce5a2082\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91ac8cdd8a96ddf48c8d50b3af514b54\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91ac8cdd8a96ddf48c8d50b3af514b54\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b5e08ae6a38078aa05566caa111bb4f\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b5e08ae6a38078aa05566caa111bb4f\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\289686107515fa75e5758775a2ecfe1c\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\289686107515fa75e5758775a2ecfe1c\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7f4358a560a3680df65cb14f7c0817b\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7f4358a560a3680df65cb14f7c0817b\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b4fd7fcef298b41f39dafcff5eb19bb\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b4fd7fcef298b41f39dafcff5eb19bb\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7447cd5c348eea0fe1634dc086b66f6b\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7447cd5c348eea0fe1634dc086b66f6b\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ea2ee4009397bd80cf92b97630d3a18\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ea2ee4009397bd80cf92b97630d3a18\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6be99e8ef4ebb99e2a8673e7c3bd48fe\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6be99e8ef4ebb99e2a8673e7c3bd48fe\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d531cb9ac8d38fdf826cdbc25dccf69e\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d531cb9ac8d38fdf826cdbc25dccf69e\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43074edfa33b3bb9c2133a6549103b9d\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43074edfa33b3bb9c2133a6549103b9d\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8789f97de103d1b7eb8a581f0f0be62f\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8789f97de103d1b7eb8a581f0f0be62f\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4cda80c164b28eda1339d4420cadeea\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4cda80c164b28eda1339d4420cadeea\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7613315b5faa388b6f97244de915db\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7613315b5faa388b6f97244de915db\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2694e11f3e1288e10fd8bc7bc531d35\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2694e11f3e1288e10fd8bc7bc531d35\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1faf5a170f74800c73eaede5aace2a36\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1faf5a170f74800c73eaede5aace2a36\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0d999e9958faa1e2bd73fcbc40c54a\transformed\lifecycle-livedata-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0d999e9958faa1e2bd73fcbc40c54a\transformed\lifecycle-livedata-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19877fa616da5a98a8488ccaa7bab822\transformed\lifecycle-livedata-core-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19877fa616da5a98a8488ccaa7bab822\transformed\lifecycle-livedata-core-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d80ddf48e8b7a7ef835f9f399aae7\transformed\lifecycle-viewmodel-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d80ddf48e8b7a7ef835f9f399aae7\transformed\lifecycle-viewmodel-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ee674f951387fdad8e027553435e743\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ee674f951387fdad8e027553435e743\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46a005f7db48e5818730cc8ce8de261\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46a005f7db48e5818730cc8ce8de261\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9de44a3d2980beab9460cb55354ba2\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9de44a3d2980beab9460cb55354ba2\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.0\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd9aa59b601d7b5b4fa3318dac2a9fd6\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd9aa59b601d7b5b4fa3318dac2a9fd6\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da95d32215f7265175208a51488cbca8\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da95d32215f7265175208a51488cbca8\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common-jvm:2.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common-jvm\2.7.1\46a9845444d0b849131baeb85bfe4051828261ad\room-common-jvm-2.7.1.jar"
      resolved="androidx.room:room-common-jvm:2.7.1"/>
  <library
      name="androidx.room:room-runtime-android:2.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\jars\classes.jar"
      resolved="androidx.room:room-runtime-android:2.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d27ebbc30fdc3e519f7a53d3f50f6e1\transformed\room-ktx-2.7.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d27ebbc30fdc3e519f7a53d3f50f6e1\transformed\room-ktx-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.insert-koin:koin-compose-jvm:4.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-compose-jvm\4.0.4\f1c0d0db29ad3cecfa6c857643a6581b37a1f284\koin-compose-jvm-4.0.4.jar"
      resolved="io.insert-koin:koin-compose-jvm:4.0.4"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c47c2f0d707a77500de5d16603646604\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c47c2f0d707a77500de5d16603646604\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65446332b6e7655e06a8244a1908e386\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65446332b6e7655e06a8244a1908e386\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6785009020cf6f655fa23647c70ce01f\transformed\datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6785009020cf6f655fa23647c70ce01f\transformed\datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acff7f57bc3a7b0b7a9c3a6fcde4195\transformed\datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acff7f57bc3a7b0b7a9c3a6fcde4195\transformed\datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.6\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.6.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.6"/>
  <library
      name="androidx.datastore:datastore-preferences-core-android:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8a76b5d545c6e3de73217e505982d96\transformed\datastore-preferences-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-core-android:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8a76b5d545c6e3de73217e505982d96\transformed\datastore-preferences-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\314f2bb57d677f489396bb6ceb95130d\transformed\datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\314f2bb57d677f489396bb6ceb95130d\transformed\datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.10.2\4a9f78ef49483748e2c129f3d124b8fa249dafbf\kotlinx-coroutines-core-jvm-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.10.2\c8705d275d81f19e8afaf3ff9b5bf7a4b6b6c19b\kotlinx-coroutines-android-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-datetime-jvm\0.6.2\f177b43ce53f69151797206ce762b36199692474\kotlinx-datetime-jvm-0.6.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.8.1\510cb839cce9a3e708052d480a6fbf4a7274dfcd\kotlinx-serialization-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.8.1\4de3bace4b175753df5484d2acd74c14bfeb5be9\kotlinx-serialization-json-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.1"/>
  <library
      name="com.mikepenz:aboutlibraries-compose-android:12.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a78baa1dd3b10408707cac07e3dcb302\transformed\aboutlibraries-compose-m2-release\jars\classes.jar"
      resolved="com.mikepenz:aboutlibraries-compose-android:12.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a78baa1dd3b10408707cac07e3dcb302\transformed\aboutlibraries-compose-m2-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.mikepenz:aboutlibraries-compose-core-android:12.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4147f5993b357fe1d0697c3486956871\transformed\aboutlibraries-compose-release\jars\classes.jar"
      resolved="com.mikepenz:aboutlibraries-compose-core-android:12.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4147f5993b357fe1d0697c3486956871\transformed\aboutlibraries-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.mikepenz:aboutlibraries-core-android:12.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b555e4b9aa1f401e3e4ce1874d2684c\transformed\aboutlibraries-core-release\jars\classes.jar"
      resolved="com.mikepenz:aboutlibraries-core-android:12.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b555e4b9aa1f401e3e4ce1874d2684c\transformed\aboutlibraries-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c508acb49021f88dea9e4626113fe1\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c307f8b36f787856618f98680b7fb881\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c307f8b36f787856618f98680b7fb881\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff94553f33284d8531948ffac8ad1344\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff94553f33284d8531948ffac8ad1344\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-ktx:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.5.0\27c78a926a16a1bf792b2285cf2834e8caae4a07\collection-ktx-1.5.0.jar"
      resolved="androidx.collection:collection-ktx:1.5.0"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"/>
  <library
      name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df7267b64c2269cb11b82c5df7472cfe\transformed\sqlite-framework-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework-android:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df7267b64c2269cb11b82c5df7472cfe\transformed\sqlite-framework-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-android:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b7555004e4ced5c70f95030b892b91\transformed\sqlite-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-android:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b7555004e4ced5c70f95030b892b91\transformed\sqlite-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44f61f7ff6723e3d17f6d5d34eae84b\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44f61f7ff6723e3d17f6d5d34eae84b\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaf76573ae95560643cd0ec6870fd644\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaf76573ae95560643cd0ec6870fd644\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30c45852bec890e2b4f57334c9f61253\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30c45852bec890e2b4f57334c9f61253\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.insert-koin:koin-core-jvm:4.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\4.0.4\a846d2959adbdc88ff6b94110d32561f65bb4777\koin-core-jvm-4.0.4.jar"
      resolved="io.insert-koin:koin-core-jvm:4.0.4"/>
  <library
      name="sh.calvin.reorderable:reorderable-android-debug:2.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28b430625e95313c16f6ec8453ea0b40\transformed\reorderable-debug\jars\classes.jar"
      resolved="sh.calvin.reorderable:reorderable-android-debug:2.4.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28b430625e95313c16f6ec8453ea0b40\transformed\reorderable-debug"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.0\e000bd084353d84c9e888f6fb341dc1f5b79d948\kotlin-stdlib-jdk8-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0"/>
  <library
      name="org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0cacda2f573a4bd19f2f5f7856b2bea\transformed\core-uri-debug\jars\classes.jar"
      resolved="org.jetbrains.androidx.core:core-uri-android-debug:1.1.0-alpha03"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0cacda2f573a4bd19f2f5f7856b2bea\transformed\core-uri-debug"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.0\f320478990d05e0cfaadd74f9619fd6027adbf37\kotlin-stdlib-jdk7-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-collections-immutable-jvm\0.3.8\6119a57df1ad662593dfe21954898183c6c933b4\kotlinx-collections-immutable-jvm-0.3.8.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.8"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.20\aa8ca79cd50578314f6d1180c47cbe14c0fee567\kotlin-stdlib-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.20"/>
  <library
      name="com.google.dagger:hilt-core:2.49@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.49\dec1221f1b48d8e46f38bcc1953dac3b74a4641f\hilt-core-2.49.jar"
      resolved="com.google.dagger:hilt-core:2.49"/>
  <library
      name="com.google.dagger:dagger:2.49@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.49\97fd74aa05761e8ea9bb5d02245f39eae30b5483\dagger-2.49.jar"
      resolved="com.google.dagger:dagger:2.49"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49452588e53587a24be93a503172f088\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.49@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197f7c6ddef0d1025c3a00e18d31a93f\transformed\dagger-lint-aar-2.49\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.49"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197f7c6ddef0d1025c3a00e18d31a93f\transformed\dagger-lint-aar-2.49"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="androidx.glance:glance-appwidget-external-protobuf:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.glance\glance-appwidget-external-protobuf\1.1.1\6d2bcb8fb2d00a5513cf681bb2756be459d21b78\glance-appwidget-external-protobuf-1.1.1.jar"
      resolved="androidx.glance:glance-appwidget-external-protobuf:1.1.1"/>
  <library
      name="androidx.glance:glance-appwidget-proto:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.glance\glance-appwidget-proto\1.1.1\a509ef84a63e2584c5afae6e1d5a757e1bae4418\glance-appwidget-proto-1.1.1.jar"
      resolved="androidx.glance:glance-appwidget-proto:1.1.1"/>
  <library
      name="androidx.compose.material:material-android:1.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\834bf615f3749af516fa6fc397086833\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\834bf615f3749af516fa6fc397086833\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e70223b363bd5f99b039ada74af213\transformed\ui-backhandler-debug\jars\classes.jar"
      resolved="org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0-alpha03"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8e70223b363bd5f99b039ada74af213\transformed\ui-backhandler-debug"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-remoteviews:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\jars\classes.jar"
      resolved="androidx.core:core-remoteviews:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057c67d4fe2ed6b6fe8bb62a6954b32d\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057c67d4fe2ed6b6fe8bb62a6954b32d\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7308887013a2be47fde69de0853200e\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7308887013a2be47fde69de0853200e\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809af1d6ad911492049172922dbfa13f\transformed\emoji2-views-helper-1.4.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809af1d6ad911492049172922dbfa13f\transformed\emoji2-views-helper-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a87736fc935020070d380641e9b83fa\transformed\savedstate-compose-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-compose-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a87736fc935020070d380641e9b83fa\transformed\savedstate-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316788cae763d29a5e4bef4741d327c3\transformed\lifecycle-service-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316788cae763d29a5e4bef4741d327c3\transformed\lifecycle-service-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52981bd7c16503ab36565c23f35603a4\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52981bd7c16503ab36565c23f35603a4\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.6\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.6.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.6"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.6\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.6.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.6"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab01b3490380ba3fd94dd806914b1fa3\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab01b3490380ba3fd94dd806914b1fa3\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df142642591b39aa45c9434828573233\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df142642591b39aa45c9434828573233\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="co.touchlab:stately-concurrent-collections-jvm:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrent-collections-jvm\2.1.0\5c03b644537d7926a8f77e5735a8bebb55dafdcd\stately-concurrent-collections-jvm-2.1.0.jar"
      resolved="co.touchlab:stately-concurrent-collections-jvm:2.1.0"/>
  <library
      name="co.touchlab:stately-concurrency-jvm:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrency-jvm\2.1.0\6285428408c4d7e4a6d9a09511de877103effd81\stately-concurrency-jvm-2.1.0.jar"
      resolved="co.touchlab:stately-concurrency-jvm:2.1.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlinx:atomicfu-jvm:0.23.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\atomicfu-jvm\0.23.2\a4601dc42dceb031a586058e8356ff778a57dea0\atomicfu-jvm-0.23.2.jar"
      resolved="org.jetbrains.kotlinx:atomicfu-jvm:0.23.2"/>
  <library
      name="androidx.performance:performance-annotation-android:1.0.0-alpha01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc03fa3297ec9632d18b8ca726fadd49\transformed\performance-annotation-release\jars\classes.jar"
      resolved="androidx.performance:performance-annotation-android:1.0.0-alpha01"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc03fa3297ec9632d18b8ca726fadd49\transformed\performance-annotation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="co.touchlab:stately-strict-jvm:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-strict-jvm\2.1.0\3ae8369209065455f4630793bf6aca0fb88c8b6b\stately-strict-jvm-2.1.0.jar"
      resolved="co.touchlab:stately-strict-jvm:2.1.0"/>
</libraries>
