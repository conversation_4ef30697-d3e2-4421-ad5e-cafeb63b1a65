  Activity android.app  Application android.app  AppWidgetManager android.app.Activity  
ChromeOSTheme android.app.Activity  	Exception android.app.Activity  Intent android.app.Activity  
MainScreen android.app.Activity  Modifier android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  Scaffold android.app.Activity  
WeatherWidget android.app.Activity  WidgetConfigActivity android.app.Activity  WidgetConfigScreen android.app.Activity  WidgetConfigViewModel android.app.Activity  android android.app.Activity  appWidgetId android.app.Activity  apply android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  getValue android.app.Activity  inject android.app.Activity  intent android.app.Activity  java android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  parametersOf android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  	setResult android.app.Activity  
startActivity android.app.Activity  	updateAll android.app.Activity  Level android.app.Application  androidContext android.app.Application  
androidLogger android.app.Application  databaseModule android.app.Application  onCreate android.app.Application  repositoryModule android.app.Application  	startKoin android.app.Application  
useCaseModule android.app.Application  viewModelModule android.app.Application  AppWidgetManager android.appwidget  EXTRA_APPWIDGET_ID "android.appwidget.AppWidgetManager  INVALID_APPWIDGET_ID "android.appwidget.AppWidgetManager  ACTION_UPDATE_WEATHER #android.appwidget.AppWidgetProvider  ExistingWorkPolicy #android.appwidget.AppWidgetProvider  OneTimeWorkRequestBuilder #android.appwidget.AppWidgetProvider  WEATHER_UPDATE_WORK_NAME #android.appwidget.AppWidgetProvider  WeatherUpdateWorker #android.appwidget.AppWidgetProvider  
WeatherWidget #android.appwidget.AppWidgetProvider  WorkManager #android.appwidget.AppWidgetProvider  getInstance #android.appwidget.AppWidgetProvider  Context android.content  Intent android.content  ACTION_UPDATE_WEATHER !android.content.BroadcastReceiver  ExistingWorkPolicy !android.content.BroadcastReceiver  OneTimeWorkRequestBuilder !android.content.BroadcastReceiver  WEATHER_UPDATE_WORK_NAME !android.content.BroadcastReceiver  WeatherUpdateWorker !android.content.BroadcastReceiver  
WeatherWidget !android.content.BroadcastReceiver  WorkManager !android.content.BroadcastReceiver  getInstance !android.content.BroadcastReceiver  AppWidgetManager android.content.Context  
ChromeOSTheme android.content.Context  	Exception android.content.Context  Intent android.content.Context  Level android.content.Context  
MainScreen android.content.Context  Modifier android.content.Context  RESULT_CANCELED android.content.Context  	RESULT_OK android.content.Context  Scaffold android.content.Context  
WeatherWidget android.content.Context  WidgetConfigActivity android.content.Context  WidgetConfigScreen android.content.Context  WidgetConfigViewModel android.content.Context  android android.content.Context  androidContext android.content.Context  
androidLogger android.content.Context  appWidgetId android.content.Context  applicationContext android.content.Context  apply android.content.Context  	dataStore android.content.Context  databaseModule android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getValue android.content.Context  inject android.content.Context  java android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  padding android.content.Context  parametersOf android.content.Context  provideDelegate android.content.Context  repositoryModule android.content.Context  
setContent android.content.Context  	setResult android.content.Context  	startKoin android.content.Context  	updateAll android.content.Context  
useCaseModule android.content.Context  viewModelModule android.content.Context  AppWidgetManager android.content.ContextWrapper  
ChromeOSTheme android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  Level android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  Modifier android.content.ContextWrapper  RESULT_CANCELED android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  Scaffold android.content.ContextWrapper  
WeatherWidget android.content.ContextWrapper  WidgetConfigActivity android.content.ContextWrapper  WidgetConfigScreen android.content.ContextWrapper  WidgetConfigViewModel android.content.ContextWrapper  android android.content.ContextWrapper  androidContext android.content.ContextWrapper  
androidLogger android.content.ContextWrapper  appWidgetId android.content.ContextWrapper  apply android.content.ContextWrapper  databaseModule android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  getValue android.content.ContextWrapper  inject android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  padding android.content.ContextWrapper  parametersOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  repositoryModule android.content.ContextWrapper  
setContent android.content.ContextWrapper  	setResult android.content.ContextWrapper  	startKoin android.content.ContextWrapper  	updateAll android.content.ContextWrapper  
useCaseModule android.content.ContextWrapper  viewModelModule android.content.ContextWrapper  AppWidgetManager android.content.Intent  action android.content.Intent  android android.content.Intent  appWidgetId android.content.Intent  apply android.content.Intent  extras android.content.Intent  putExtra android.content.Intent  Build 
android.os  Bundle 
android.os  getInt android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  getInt android.os.Bundle  AppWidgetManager  android.view.ContextThemeWrapper  
ChromeOSTheme  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  RESULT_CANCELED  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  
WeatherWidget  android.view.ContextThemeWrapper  WidgetConfigActivity  android.view.ContextThemeWrapper  WidgetConfigScreen  android.view.ContextThemeWrapper  WidgetConfigViewModel  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  appWidgetId  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  inject  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  parametersOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  	setResult  android.view.ContextThemeWrapper  	updateAll  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AppWidgetManager #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
ChromeOSTheme #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  GetWeatherDataUseCase #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  RESULT_CANCELED #androidx.activity.ComponentActivity  	RESULT_OK #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  UpdateWidgetConfigUseCase #androidx.activity.ComponentActivity  
WeatherWidget #androidx.activity.ComponentActivity  WidgetConfigActivity #androidx.activity.ComponentActivity  WidgetConfigScreen #androidx.activity.ComponentActivity  WidgetConfigViewModel #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  appWidgetId #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  inject #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  parametersOf #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	setResult #androidx.activity.ComponentActivity  	updateAll #androidx.activity.ComponentActivity  AppWidgetManager -androidx.activity.ComponentActivity.Companion  
ChromeOSTheme -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  RESULT_CANCELED -androidx.activity.ComponentActivity.Companion  	RESULT_OK -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  
WeatherWidget -androidx.activity.ComponentActivity.Companion  WidgetConfigActivity -androidx.activity.ComponentActivity.Companion  WidgetConfigScreen -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  appWidgetId -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  inject -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  parametersOf -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  	setResult -androidx.activity.ComponentActivity.Companion  	updateAll -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AdditionalOptionsSection "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  
ChromeOSTheme "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  CitySelectionSection "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  FontSize "androidx.compose.foundation.layout  FontSizeSliderSection "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  IconSize "androidx.compose.foundation.layout  IconSizeSliderSection "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  PreviewSection "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  WeatherCondition "androidx.compose.foundation.layout  WeatherData "androidx.compose.foundation.layout  WidgetConfigActivity "androidx.compose.foundation.layout  WidgetConfigViewModel "androidx.compose.foundation.layout  WidgetConfiguration "androidx.compose.foundation.layout  WidgetPreviewComponent "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  getWeatherEmoji "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  io "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  String +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  WidgetPreviewComponent +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  format +androidx.compose.foundation.layout.BoxScope  getWeatherEmoji +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  AdditionalOptionsSection .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  CitySelectionSection .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  FontSizeSliderSection .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  IconSizeSliderSection .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  PreviewSection .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  WidgetPreviewComponent .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  getWeatherEmoji .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
despicable %androidx.compose.foundation.layout.io  chromeos 0androidx.compose.foundation.layout.io.despicable  domain 9androidx.compose.foundation.layout.io.despicable.chromeos  model @androidx.compose.foundation.layout.io.despicable.chromeos.domain  WeatherData Fandroidx.compose.foundation.layout.io.despicable.chromeos.domain.model  WidgetConfiguration Fandroidx.compose.foundation.layout.io.despicable.chromeos.domain.model  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AdditionalOptionsSection .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyItemScope  CitySelectionSection .androidx.compose.foundation.lazy.LazyItemScope  
FilterChip .androidx.compose.foundation.lazy.LazyItemScope  FontSizeSliderSection .androidx.compose.foundation.lazy.LazyItemScope  IconSizeSliderSection .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  PreviewSection .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  AdditionalOptionsSection .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyListScope  CitySelectionSection .androidx.compose.foundation.lazy.LazyListScope  
FilterChip .androidx.compose.foundation.lazy.LazyListScope  FontSizeSliderSection .androidx.compose.foundation.lazy.LazyListScope  IconSizeSliderSection .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  PreviewSection .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  RoundedCornerShape !androidx.compose.foundation.shape  AdditionalOptionsSection androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  
ChromeOSTheme androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  CitySelectionSection androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FilterChip androidx.compose.material3  Float androidx.compose.material3  FontSize androidx.compose.material3  FontSizeSliderSection androidx.compose.material3  
FontWeight androidx.compose.material3  IconSize androidx.compose.material3  IconSizeSliderSection androidx.compose.material3  Intent androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  List androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  Preview androidx.compose.material3  PreviewSection androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Slider androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  WeatherCondition androidx.compose.material3  WeatherData androidx.compose.material3  WidgetConfigActivity androidx.compose.material3  WidgetConfigViewModel androidx.compose.material3  WidgetConfiguration androidx.compose.material3  WidgetPreviewComponent androidx.compose.material3  android androidx.compose.material3  apply androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  collectAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  getWeatherEmoji androidx.compose.material3  height androidx.compose.material3  io androidx.compose.material3  java androidx.compose.material3  lightColorScheme androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  rangeTo androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
despicable androidx.compose.material3.io  chromeos (androidx.compose.material3.io.despicable  domain 1androidx.compose.material3.io.despicable.chromeos  model 8androidx.compose.material3.io.despicable.chromeos.domain  WeatherData >androidx.compose.material3.io.despicable.chromeos.domain.model  WidgetConfiguration >androidx.compose.material3.io.despicable.chromeos.domain.model  AdditionalOptionsSection androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  CitySelectionSection androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FilterChip androidx.compose.runtime  Float androidx.compose.runtime  FontSize androidx.compose.runtime  FontSizeSliderSection androidx.compose.runtime  
FontWeight androidx.compose.runtime  IconSize androidx.compose.runtime  IconSizeSliderSection androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  OptIn androidx.compose.runtime  PreviewSection androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Slider androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Switch androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  WidgetConfigViewModel androidx.compose.runtime  WidgetPreviewComponent androidx.compose.runtime  collectAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  io androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  rangeTo androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  
despicable androidx.compose.runtime.io  chromeos &androidx.compose.runtime.io.despicable  domain /androidx.compose.runtime.io.despicable.chromeos  model 6androidx.compose.runtime.io.despicable.chromeos.domain  WeatherData <androidx.compose.runtime.io.despicable.chromeos.domain.model  WidgetConfiguration <androidx.compose.runtime.io.despicable.chromeos.domain.model  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  DarkGray "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  DarkGray ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  Role androidx.compose.ui.semantics  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AppWidgetManager #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ChromeOSTheme #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  GetWeatherDataUseCase #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  RESULT_CANCELED #androidx.core.app.ComponentActivity  	RESULT_OK #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  UpdateWidgetConfigUseCase #androidx.core.app.ComponentActivity  
WeatherWidget #androidx.core.app.ComponentActivity  WidgetConfigActivity #androidx.core.app.ComponentActivity  WidgetConfigScreen #androidx.core.app.ComponentActivity  WidgetConfigViewModel #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  appWidgetId #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  inject #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  parametersOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	setResult #androidx.core.app.ComponentActivity  	updateAll #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  Boolean #androidx.datastore.preferences.core  Context #androidx.datastore.preferences.core  	DataStore #androidx.datastore.preferences.core  Flow #androidx.datastore.preferences.core  FontSize #androidx.datastore.preferences.core  IconSize #androidx.datastore.preferences.core  Inject #androidx.datastore.preferences.core  Int #androidx.datastore.preferences.core  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  	Singleton #androidx.datastore.preferences.core  System #androidx.datastore.preferences.core  WidgetConfiguration #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  cityNameKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  fontSizeKey #androidx.datastore.preferences.core  iconSizeKey #androidx.datastore.preferences.core  lastUpdatedKey #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  map #androidx.datastore.preferences.core  preferencesDataStore #androidx.datastore.preferences.core  provideDelegate #androidx.datastore.preferences.core  showHumidityKey #androidx.datastore.preferences.core  showWindSpeedKey #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  remove 6androidx.datastore.preferences.core.MutablePreferences  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  contains /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  GlanceId androidx.glance  GlanceModifier androidx.glance  GlanceTheme androidx.glance  
background androidx.glance  toString androidx.glance.GlanceId  	Companion androidx.glance.GlanceModifier  
background androidx.glance.GlanceModifier  fillMaxSize androidx.glance.GlanceModifier  fillMaxWidth androidx.glance.GlanceModifier  height androidx.glance.GlanceModifier  padding androidx.glance.GlanceModifier  width androidx.glance.GlanceModifier  fillMaxSize (androidx.glance.GlanceModifier.Companion  fillMaxWidth (androidx.glance.GlanceModifier.Companion  height (androidx.glance.GlanceModifier.Companion  width (androidx.glance.GlanceModifier.Companion  GlanceAppWidget androidx.glance.appwidget  GlanceAppWidgetReceiver androidx.glance.appwidget  provideContent androidx.glance.appwidget  	updateAll androidx.glance.appwidget  ACTION_UPDATE_WEATHER 1androidx.glance.appwidget.GlanceAppWidgetReceiver  AppWidgetManager 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	Companion 1androidx.glance.appwidget.GlanceAppWidgetReceiver  Context 1androidx.glance.appwidget.GlanceAppWidgetReceiver  ExistingWorkPolicy 1androidx.glance.appwidget.GlanceAppWidgetReceiver  GlanceAppWidget 1androidx.glance.appwidget.GlanceAppWidgetReceiver  IntArray 1androidx.glance.appwidget.GlanceAppWidgetReceiver  Intent 1androidx.glance.appwidget.GlanceAppWidgetReceiver  OneTimeWorkRequestBuilder 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WEATHER_UPDATE_WORK_NAME 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WeatherUpdateWorker 1androidx.glance.appwidget.GlanceAppWidgetReceiver  
WeatherWidget 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WorkManager 1androidx.glance.appwidget.GlanceAppWidgetReceiver  getInstance 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	onDeleted 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	onReceive 1androidx.glance.appwidget.GlanceAppWidgetReceiver  onUpdate 1androidx.glance.appwidget.GlanceAppWidgetReceiver  ACTION_UPDATE_WEATHER ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  ExistingWorkPolicy ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  OneTimeWorkRequestBuilder ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  WEATHER_UPDATE_WORK_NAME ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  
WeatherWidget ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  WorkManager ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  getInstance ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  	Alignment androidx.glance.layout  Box androidx.glance.layout  Color androidx.glance.layout  
ColorProvider androidx.glance.layout  Column androidx.glance.layout  ColumnScope androidx.glance.layout  
Composable androidx.glance.layout  Context androidx.glance.layout  	Exception androidx.glance.layout  
FontWeight androidx.glance.layout  GlanceAppWidget androidx.glance.layout  GlanceId androidx.glance.layout  GlanceModifier androidx.glance.layout  GlanceTheme androidx.glance.layout  Int androidx.glance.layout  Row androidx.glance.layout  RowScope androidx.glance.layout  SampleWeatherData androidx.glance.layout  Spacer androidx.glance.layout  String androidx.glance.layout  Text androidx.glance.layout  	TextStyle androidx.glance.layout  WeatherCondition androidx.glance.layout  WeatherData androidx.glance.layout  WeatherDatabase androidx.glance.layout  WidgetConfiguration androidx.glance.layout  WidgetPreferences androidx.glance.layout  
background androidx.glance.layout  fillMaxSize androidx.glance.layout  fillMaxWidth androidx.glance.layout  first androidx.glance.layout  format androidx.glance.layout  generateRandomWeatherData androidx.glance.layout  getDatabase androidx.glance.layout  getWeatherEmoji androidx.glance.layout  height androidx.glance.layout  padding androidx.glance.layout  
toDomainModel androidx.glance.layout  width androidx.glance.layout  Center  androidx.glance.layout.Alignment  CenterHorizontally  androidx.glance.layout.Alignment  CenterVertically  androidx.glance.layout.Alignment  	Companion  androidx.glance.layout.Alignment  
Horizontal  androidx.glance.layout.Alignment  Vertical  androidx.glance.layout.Alignment  Center *androidx.glance.layout.Alignment.Companion  CenterHorizontally *androidx.glance.layout.Alignment.Companion  CenterVertically *androidx.glance.layout.Alignment.Companion  	Alignment "androidx.glance.layout.ColumnScope  Color "androidx.glance.layout.ColumnScope  
ColorProvider "androidx.glance.layout.ColumnScope  
FontWeight "androidx.glance.layout.ColumnScope  GlanceModifier "androidx.glance.layout.ColumnScope  Row "androidx.glance.layout.ColumnScope  Spacer "androidx.glance.layout.ColumnScope  String "androidx.glance.layout.ColumnScope  Text "androidx.glance.layout.ColumnScope  	TextStyle "androidx.glance.layout.ColumnScope  dp "androidx.glance.layout.ColumnScope  fillMaxWidth "androidx.glance.layout.ColumnScope  format "androidx.glance.layout.ColumnScope  getWeatherEmoji "androidx.glance.layout.ColumnScope  height "androidx.glance.layout.ColumnScope  sp "androidx.glance.layout.ColumnScope  width "androidx.glance.layout.ColumnScope  Color androidx.glance.layout.RowScope  
ColorProvider androidx.glance.layout.RowScope  GlanceModifier androidx.glance.layout.RowScope  Spacer androidx.glance.layout.RowScope  String androidx.glance.layout.RowScope  Text androidx.glance.layout.RowScope  	TextStyle androidx.glance.layout.RowScope  dp androidx.glance.layout.RowScope  format androidx.glance.layout.RowScope  sp androidx.glance.layout.RowScope  width androidx.glance.layout.RowScope  
FontWeight androidx.glance.text  Text androidx.glance.text  	TextStyle androidx.glance.text  Bold androidx.glance.text.FontWeight  	Companion androidx.glance.text.FontWeight  Bold )androidx.glance.text.FontWeight.Companion  
ColorProvider androidx.glance.unit  LifecycleCoroutineScope androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  EntityInsertAdapter 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  InvalidationTracker 
androidx.room  List 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  String 
androidx.room  
WeatherEntity 
androidx.room  insert !androidx.room.EntityInsertAdapter  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AutoMigrationSpec androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  InvalidationTracker androidx.room.RoomDatabase  KClass androidx.room.RoomDatabase  Lazy androidx.room.RoomDatabase  List androidx.room.RoomDatabase  Map androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  MutableList androidx.room.RoomDatabase  
MutableMap androidx.room.RoomDatabase  
MutableSet androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  RoomOpenDelegate androidx.room.RoomDatabase  SQLiteConnection androidx.room.RoomDatabase  Set androidx.room.RoomDatabase  String androidx.room.RoomDatabase  	TableInfo androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
WeatherDao androidx.room.RoomDatabase  WeatherDao_Impl androidx.room.RoomDatabase  WeatherDatabase androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  dropFtsSyncTriggers androidx.room.RoomDatabase  execSQL androidx.room.RoomDatabase  getRequiredConverters androidx.room.RoomDatabase  internalInitInvalidationTracker androidx.room.RoomDatabase  java androidx.room.RoomDatabase  lazy androidx.room.RoomDatabase  
mutableListOf androidx.room.RoomDatabase  mutableMapOf androidx.room.RoomDatabase  mutableSetOf androidx.room.RoomDatabase  performClear androidx.room.RoomDatabase  read androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  
trimMargin androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  InvalidationTracker $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  RoomOpenDelegate $androidx.room.RoomDatabase.Companion  	TableInfo $androidx.room.RoomDatabase.Companion  
WeatherDao $androidx.room.RoomDatabase.Companion  WeatherDao_Impl $androidx.room.RoomDatabase.Companion  WeatherDatabase $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  dropFtsSyncTriggers $androidx.room.RoomDatabase.Companion  execSQL $androidx.room.RoomDatabase.Companion  getRequiredConverters $androidx.room.RoomDatabase.Companion  internalInitInvalidationTracker $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  lazy $androidx.room.RoomDatabase.Companion  
mutableListOf $androidx.room.RoomDatabase.Companion  mutableMapOf $androidx.room.RoomDatabase.Companion  mutableSetOf $androidx.room.RoomDatabase.Companion  read $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  
trimMargin $androidx.room.RoomDatabase.Companion  ValidationResult +androidx.room.RoomDatabase.RoomOpenDelegate  Column $androidx.room.RoomDatabase.TableInfo  
ForeignKey $androidx.room.RoomDatabase.TableInfo  Index $androidx.room.RoomDatabase.TableInfo  
MutableMap androidx.room.RoomOpenDelegate  
MutableSet androidx.room.RoomOpenDelegate  RoomOpenDelegate androidx.room.RoomOpenDelegate  SQLiteConnection androidx.room.RoomOpenDelegate  String androidx.room.RoomOpenDelegate  	TableInfo androidx.room.RoomOpenDelegate  ValidationResult androidx.room.RoomOpenDelegate  dropFtsSyncTriggers androidx.room.RoomOpenDelegate  execSQL androidx.room.RoomOpenDelegate  internalInitInvalidationTracker androidx.room.RoomOpenDelegate  mutableMapOf androidx.room.RoomOpenDelegate  mutableSetOf androidx.room.RoomOpenDelegate  read androidx.room.RoomOpenDelegate  
trimMargin androidx.room.RoomOpenDelegate  ValidationResult /androidx.room.RoomOpenDelegate.RoomOpenDelegate  Column (androidx.room.RoomOpenDelegate.TableInfo  
ForeignKey (androidx.room.RoomOpenDelegate.TableInfo  Index (androidx.room.RoomOpenDelegate.TableInfo  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  CREATED_FROM_ENTITY androidx.room.util.TableInfo  Column androidx.room.util.TableInfo  	Companion androidx.room.util.TableInfo  
ForeignKey androidx.room.util.TableInfo  Index androidx.room.util.TableInfo  equals androidx.room.util.TableInfo  CREATED_FROM_ENTITY &androidx.room.util.TableInfo.Companion  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  execSQL  androidx.sqlite.SQLiteConnection  prepare  androidx.sqlite.SQLiteConnection  
bindDouble androidx.sqlite.SQLiteStatement  bindLong androidx.sqlite.SQLiteStatement  bindText androidx.sqlite.SQLiteStatement  close androidx.sqlite.SQLiteStatement  	getDouble androidx.sqlite.SQLiteStatement  getLong androidx.sqlite.SQLiteStatement  getText androidx.sqlite.SQLiteStatement  step androidx.sqlite.SQLiteStatement  CoroutineWorker 
androidx.work  ExistingWorkPolicy 
androidx.work  OneTimeWorkRequest 
androidx.work  OneTimeWorkRequestBuilder 
androidx.work  	Operation 
androidx.work  WorkManager 
androidx.work  WorkerParameters 
androidx.work  Context androidx.work.CoroutineWorker  	Exception androidx.work.CoroutineWorker  GetWeatherDataUseCase androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  
WeatherWidget androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  getValue androidx.work.CoroutineWorker  inject androidx.work.CoroutineWorker  provideDelegate androidx.work.CoroutineWorker  	updateAll androidx.work.CoroutineWorker  REPLACE  androidx.work.ExistingWorkPolicy  	Exception androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  
WeatherWidget androidx.work.ListenableWorker  applicationContext androidx.work.ListenableWorker  getValue androidx.work.ListenableWorker  inject androidx.work.ListenableWorker  provideDelegate androidx.work.ListenableWorker  	updateAll androidx.work.ListenableWorker  retry %androidx.work.ListenableWorker.Result  success %androidx.work.ListenableWorker.Result  Builder  androidx.work.OneTimeWorkRequest  build (androidx.work.OneTimeWorkRequest.Builder  	Companion androidx.work.WorkManager  enqueueUniqueWork androidx.work.WorkManager  getInstance androidx.work.WorkManager  getInstance #androidx.work.WorkManager.Companion  	Alignment io.despicable.chromeos  Application io.despicable.chromeos  Arrangement io.despicable.chromeos  Bundle io.despicable.chromeos  Button io.despicable.chromeos  
ChromeOSTheme io.despicable.chromeos  Column io.despicable.chromeos  ComponentActivity io.despicable.chromeos  
Composable io.despicable.chromeos  
FontWeight io.despicable.chromeos  Intent io.despicable.chromeos  Level io.despicable.chromeos  MainActivity io.despicable.chromeos  
MainScreen io.despicable.chromeos  MainScreenPreview io.despicable.chromeos  
MaterialTheme io.despicable.chromeos  Modifier io.despicable.chromeos  Preview io.despicable.chromeos  Scaffold io.despicable.chromeos  Spacer io.despicable.chromeos  Text io.despicable.chromeos  Unit io.despicable.chromeos  WeatherApplication io.despicable.chromeos  WidgetConfigActivity io.despicable.chromeos  android io.despicable.chromeos  apply io.despicable.chromeos  databaseModule io.despicable.chromeos  fillMaxSize io.despicable.chromeos  fillMaxWidth io.despicable.chromeos  height io.despicable.chromeos  java io.despicable.chromeos  padding io.despicable.chromeos  repositoryModule io.despicable.chromeos  	startKoin io.despicable.chromeos  
useCaseModule io.despicable.chromeos  viewModelModule io.despicable.chromeos  
ChromeOSTheme #io.despicable.chromeos.MainActivity  Intent #io.despicable.chromeos.MainActivity  
MainScreen #io.despicable.chromeos.MainActivity  Modifier #io.despicable.chromeos.MainActivity  Scaffold #io.despicable.chromeos.MainActivity  WidgetConfigActivity #io.despicable.chromeos.MainActivity  android #io.despicable.chromeos.MainActivity  apply #io.despicable.chromeos.MainActivity  enableEdgeToEdge #io.despicable.chromeos.MainActivity  fillMaxSize #io.despicable.chromeos.MainActivity  java #io.despicable.chromeos.MainActivity  padding #io.despicable.chromeos.MainActivity  
setContent #io.despicable.chromeos.MainActivity  
startActivity #io.despicable.chromeos.MainActivity  Level )io.despicable.chromeos.WeatherApplication  androidContext )io.despicable.chromeos.WeatherApplication  
androidLogger )io.despicable.chromeos.WeatherApplication  databaseModule )io.despicable.chromeos.WeatherApplication  repositoryModule )io.despicable.chromeos.WeatherApplication  	startKoin )io.despicable.chromeos.WeatherApplication  
useCaseModule )io.despicable.chromeos.WeatherApplication  viewModelModule )io.despicable.chromeos.WeatherApplication  AutoMigrationSpec $io.despicable.chromeos.data.database  Context $io.despicable.chromeos.data.database  Database $io.despicable.chromeos.data.database  	Generated $io.despicable.chromeos.data.database  InvalidationTracker $io.despicable.chromeos.data.database  KClass $io.despicable.chromeos.data.database  Lazy $io.despicable.chromeos.data.database  List $io.despicable.chromeos.data.database  Map $io.despicable.chromeos.data.database  	Migration $io.despicable.chromeos.data.database  MutableList $io.despicable.chromeos.data.database  
MutableMap $io.despicable.chromeos.data.database  
MutableSet $io.despicable.chromeos.data.database  Room $io.despicable.chromeos.data.database  RoomDatabase $io.despicable.chromeos.data.database  RoomOpenDelegate $io.despicable.chromeos.data.database  SQLiteConnection $io.despicable.chromeos.data.database  Set $io.despicable.chromeos.data.database  String $io.despicable.chromeos.data.database  Suppress $io.despicable.chromeos.data.database  	TableInfo $io.despicable.chromeos.data.database  Volatile $io.despicable.chromeos.data.database  
WeatherDao $io.despicable.chromeos.data.database  WeatherDao_Impl $io.despicable.chromeos.data.database  WeatherDatabase $io.despicable.chromeos.data.database  WeatherDatabase_Impl $io.despicable.chromeos.data.database  
WeatherEntity $io.despicable.chromeos.data.database  databaseBuilder $io.despicable.chromeos.data.database  dropFtsSyncTriggers $io.despicable.chromeos.data.database  execSQL $io.despicable.chromeos.data.database  getRequiredConverters $io.despicable.chromeos.data.database  internalInitInvalidationTracker $io.despicable.chromeos.data.database  java $io.despicable.chromeos.data.database  lazy $io.despicable.chromeos.data.database  
mutableListOf $io.despicable.chromeos.data.database  mutableMapOf $io.despicable.chromeos.data.database  mutableSetOf $io.despicable.chromeos.data.database  read $io.despicable.chromeos.data.database  synchronized $io.despicable.chromeos.data.database  
trimMargin $io.despicable.chromeos.data.database  ValidationResult 5io.despicable.chromeos.data.database.RoomOpenDelegate  Column .io.despicable.chromeos.data.database.TableInfo  
ForeignKey .io.despicable.chromeos.data.database.TableInfo  Index .io.despicable.chromeos.data.database.TableInfo  AutoMigrationSpec 4io.despicable.chromeos.data.database.WeatherDatabase  	Companion 4io.despicable.chromeos.data.database.WeatherDatabase  Context 4io.despicable.chromeos.data.database.WeatherDatabase  
DATABASE_NAME 4io.despicable.chromeos.data.database.WeatherDatabase  INSTANCE 4io.despicable.chromeos.data.database.WeatherDatabase  InvalidationTracker 4io.despicable.chromeos.data.database.WeatherDatabase  KClass 4io.despicable.chromeos.data.database.WeatherDatabase  Lazy 4io.despicable.chromeos.data.database.WeatherDatabase  List 4io.despicable.chromeos.data.database.WeatherDatabase  Map 4io.despicable.chromeos.data.database.WeatherDatabase  	Migration 4io.despicable.chromeos.data.database.WeatherDatabase  MutableList 4io.despicable.chromeos.data.database.WeatherDatabase  
MutableMap 4io.despicable.chromeos.data.database.WeatherDatabase  
MutableSet 4io.despicable.chromeos.data.database.WeatherDatabase  Room 4io.despicable.chromeos.data.database.WeatherDatabase  RoomOpenDelegate 4io.despicable.chromeos.data.database.WeatherDatabase  SQLiteConnection 4io.despicable.chromeos.data.database.WeatherDatabase  Set 4io.despicable.chromeos.data.database.WeatherDatabase  String 4io.despicable.chromeos.data.database.WeatherDatabase  	TableInfo 4io.despicable.chromeos.data.database.WeatherDatabase  Volatile 4io.despicable.chromeos.data.database.WeatherDatabase  
WeatherDao 4io.despicable.chromeos.data.database.WeatherDatabase  WeatherDao_Impl 4io.despicable.chromeos.data.database.WeatherDatabase  WeatherDatabase 4io.despicable.chromeos.data.database.WeatherDatabase  databaseBuilder 4io.despicable.chromeos.data.database.WeatherDatabase  dropFtsSyncTriggers 4io.despicable.chromeos.data.database.WeatherDatabase  execSQL 4io.despicable.chromeos.data.database.WeatherDatabase  getDatabase 4io.despicable.chromeos.data.database.WeatherDatabase  getRequiredConverters 4io.despicable.chromeos.data.database.WeatherDatabase  internalInitInvalidationTracker 4io.despicable.chromeos.data.database.WeatherDatabase  java 4io.despicable.chromeos.data.database.WeatherDatabase  lazy 4io.despicable.chromeos.data.database.WeatherDatabase  
mutableListOf 4io.despicable.chromeos.data.database.WeatherDatabase  mutableMapOf 4io.despicable.chromeos.data.database.WeatherDatabase  mutableSetOf 4io.despicable.chromeos.data.database.WeatherDatabase  performClear 4io.despicable.chromeos.data.database.WeatherDatabase  read 4io.despicable.chromeos.data.database.WeatherDatabase  synchronized 4io.despicable.chromeos.data.database.WeatherDatabase  
trimMargin 4io.despicable.chromeos.data.database.WeatherDatabase  
weatherDao 4io.despicable.chromeos.data.database.WeatherDatabase  
DATABASE_NAME >io.despicable.chromeos.data.database.WeatherDatabase.Companion  INSTANCE >io.despicable.chromeos.data.database.WeatherDatabase.Companion  InvalidationTracker >io.despicable.chromeos.data.database.WeatherDatabase.Companion  Room >io.despicable.chromeos.data.database.WeatherDatabase.Companion  RoomOpenDelegate >io.despicable.chromeos.data.database.WeatherDatabase.Companion  	TableInfo >io.despicable.chromeos.data.database.WeatherDatabase.Companion  
WeatherDao >io.despicable.chromeos.data.database.WeatherDatabase.Companion  WeatherDao_Impl >io.despicable.chromeos.data.database.WeatherDatabase.Companion  WeatherDatabase >io.despicable.chromeos.data.database.WeatherDatabase.Companion  databaseBuilder >io.despicable.chromeos.data.database.WeatherDatabase.Companion  dropFtsSyncTriggers >io.despicable.chromeos.data.database.WeatherDatabase.Companion  execSQL >io.despicable.chromeos.data.database.WeatherDatabase.Companion  getDatabase >io.despicable.chromeos.data.database.WeatherDatabase.Companion  getRequiredConverters >io.despicable.chromeos.data.database.WeatherDatabase.Companion  internalInitInvalidationTracker >io.despicable.chromeos.data.database.WeatherDatabase.Companion  java >io.despicable.chromeos.data.database.WeatherDatabase.Companion  lazy >io.despicable.chromeos.data.database.WeatherDatabase.Companion  
mutableListOf >io.despicable.chromeos.data.database.WeatherDatabase.Companion  mutableMapOf >io.despicable.chromeos.data.database.WeatherDatabase.Companion  mutableSetOf >io.despicable.chromeos.data.database.WeatherDatabase.Companion  read >io.despicable.chromeos.data.database.WeatherDatabase.Companion  synchronized >io.despicable.chromeos.data.database.WeatherDatabase.Companion  
trimMargin >io.despicable.chromeos.data.database.WeatherDatabase.Companion  ValidationResult Eio.despicable.chromeos.data.database.WeatherDatabase.RoomOpenDelegate  Column >io.despicable.chromeos.data.database.WeatherDatabase.TableInfo  
ForeignKey >io.despicable.chromeos.data.database.WeatherDatabase.TableInfo  Index >io.despicable.chromeos.data.database.WeatherDatabase.TableInfo  InvalidationTracker 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  RoomOpenDelegate 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  	TableInfo 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  
WeatherDao 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  WeatherDao_Impl 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  _weatherDao 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  dropFtsSyncTriggers 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  execSQL 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  getRequiredConverters 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  internalInitInvalidationTracker 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  lazy 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  
mutableListOf 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  mutableMapOf 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  mutableSetOf 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  read 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  
trimMargin 9io.despicable.chromeos.data.database.WeatherDatabase_Impl  Dao (io.despicable.chromeos.data.database.dao  Double (io.despicable.chromeos.data.database.dao  EntityInsertAdapter (io.despicable.chromeos.data.database.dao  Flow (io.despicable.chromeos.data.database.dao  	Generated (io.despicable.chromeos.data.database.dao  Insert (io.despicable.chromeos.data.database.dao  Int (io.despicable.chromeos.data.database.dao  KClass (io.despicable.chromeos.data.database.dao  List (io.despicable.chromeos.data.database.dao  Long (io.despicable.chromeos.data.database.dao  MutableList (io.despicable.chromeos.data.database.dao  OnConflictStrategy (io.despicable.chromeos.data.database.dao  Query (io.despicable.chromeos.data.database.dao  RoomDatabase (io.despicable.chromeos.data.database.dao  SQLiteStatement (io.despicable.chromeos.data.database.dao  String (io.despicable.chromeos.data.database.dao  Suppress (io.despicable.chromeos.data.database.dao  Unit (io.despicable.chromeos.data.database.dao  
WeatherDao (io.despicable.chromeos.data.database.dao  WeatherDao_Impl (io.despicable.chromeos.data.database.dao  
WeatherEntity (io.despicable.chromeos.data.database.dao  arrayOf (io.despicable.chromeos.data.database.dao  
createFlow (io.despicable.chromeos.data.database.dao  	emptyList (io.despicable.chromeos.data.database.dao  getColumnIndexOrThrow (io.despicable.chromeos.data.database.dao  
mutableListOf (io.despicable.chromeos.data.database.dao  performSuspending (io.despicable.chromeos.data.database.dao  OnConflictStrategy 3io.despicable.chromeos.data.database.dao.WeatherDao  deleteWeatherByCity 3io.despicable.chromeos.data.database.dao.WeatherDao  getAllCities 3io.despicable.chromeos.data.database.dao.WeatherDao  getAllWeatherData 3io.despicable.chromeos.data.database.dao.WeatherDao  getWeatherByCity 3io.despicable.chromeos.data.database.dao.WeatherDao  getWeatherDataCount 3io.despicable.chromeos.data.database.dao.WeatherDao  insertAllWeatherData 3io.despicable.chromeos.data.database.dao.WeatherDao  insertWeatherData 3io.despicable.chromeos.data.database.dao.WeatherDao  	Companion 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  Double 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  EntityInsertAdapter 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  Flow 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  Int 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  KClass 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  List 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  Long 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  MutableList 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  RoomDatabase 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  SQLiteStatement 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  String 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  Unit 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  
WeatherEntity 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  __db 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  __insertAdapterOfWeatherEntity 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  arrayOf 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  
createFlow 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  	emptyList 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  getColumnIndexOrThrow 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  getRequiredConverters 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  
mutableListOf 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  performSuspending 8io.despicable.chromeos.data.database.dao.WeatherDao_Impl  
WeatherEntity Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  arrayOf Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  
createFlow Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  	emptyList Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  getColumnIndexOrThrow Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  getRequiredConverters Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  
mutableListOf Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  performSuspending Bio.despicable.chromeos.data.database.dao.WeatherDao_Impl.Companion  Double +io.despicable.chromeos.data.database.entity  Entity +io.despicable.chromeos.data.database.entity  Int +io.despicable.chromeos.data.database.entity  Long +io.despicable.chromeos.data.database.entity  
PrimaryKey +io.despicable.chromeos.data.database.entity  String +io.despicable.chromeos.data.database.entity  WeatherCondition +io.despicable.chromeos.data.database.entity  WeatherData +io.despicable.chromeos.data.database.entity  
WeatherEntity +io.despicable.chromeos.data.database.entity  
toDomainModel +io.despicable.chromeos.data.database.entity  toEntity +io.despicable.chromeos.data.database.entity  WeatherCondition 9io.despicable.chromeos.data.database.entity.WeatherEntity  WeatherData 9io.despicable.chromeos.data.database.entity.WeatherEntity  cityName 9io.despicable.chromeos.data.database.entity.WeatherEntity  humidity 9io.despicable.chromeos.data.database.entity.WeatherEntity  id 9io.despicable.chromeos.data.database.entity.WeatherEntity  lastUpdated 9io.despicable.chromeos.data.database.entity.WeatherEntity  temperature 9io.despicable.chromeos.data.database.entity.WeatherEntity  
toDomainModel 9io.despicable.chromeos.data.database.entity.WeatherEntity  weatherCondition 9io.despicable.chromeos.data.database.entity.WeatherEntity  	windSpeed 9io.despicable.chromeos.data.database.entity.WeatherEntity  Boolean %io.despicable.chromeos.data.datastore  Context %io.despicable.chromeos.data.datastore  	DataStore %io.despicable.chromeos.data.datastore  Flow %io.despicable.chromeos.data.datastore  FontSize %io.despicable.chromeos.data.datastore  IconSize %io.despicable.chromeos.data.datastore  Inject %io.despicable.chromeos.data.datastore  Int %io.despicable.chromeos.data.datastore  Preferences %io.despicable.chromeos.data.datastore  	Singleton %io.despicable.chromeos.data.datastore  System %io.despicable.chromeos.data.datastore  WidgetConfiguration %io.despicable.chromeos.data.datastore  WidgetPreferences %io.despicable.chromeos.data.datastore  booleanPreferencesKey %io.despicable.chromeos.data.datastore  cityNameKey %io.despicable.chromeos.data.datastore  edit %io.despicable.chromeos.data.datastore  fontSizeKey %io.despicable.chromeos.data.datastore  iconSizeKey %io.despicable.chromeos.data.datastore  lastUpdatedKey %io.despicable.chromeos.data.datastore  longPreferencesKey %io.despicable.chromeos.data.datastore  map %io.despicable.chromeos.data.datastore  preferencesDataStore %io.despicable.chromeos.data.datastore  provideDelegate %io.despicable.chromeos.data.datastore  showHumidityKey %io.despicable.chromeos.data.datastore  showWindSpeedKey %io.despicable.chromeos.data.datastore  stringPreferencesKey %io.despicable.chromeos.data.datastore  Boolean 7io.despicable.chromeos.data.datastore.WidgetPreferences  	Companion 7io.despicable.chromeos.data.datastore.WidgetPreferences  Context 7io.despicable.chromeos.data.datastore.WidgetPreferences  	DataStore 7io.despicable.chromeos.data.datastore.WidgetPreferences  Flow 7io.despicable.chromeos.data.datastore.WidgetPreferences  FontSize 7io.despicable.chromeos.data.datastore.WidgetPreferences  IconSize 7io.despicable.chromeos.data.datastore.WidgetPreferences  Inject 7io.despicable.chromeos.data.datastore.WidgetPreferences  Int 7io.despicable.chromeos.data.datastore.WidgetPreferences  Preferences 7io.despicable.chromeos.data.datastore.WidgetPreferences  System 7io.despicable.chromeos.data.datastore.WidgetPreferences  WidgetConfiguration 7io.despicable.chromeos.data.datastore.WidgetPreferences  booleanPreferencesKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  cityNameKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  context 7io.despicable.chromeos.data.datastore.WidgetPreferences  	dataStore 7io.despicable.chromeos.data.datastore.WidgetPreferences  deleteWidgetConfig 7io.despicable.chromeos.data.datastore.WidgetPreferences  edit 7io.despicable.chromeos.data.datastore.WidgetPreferences  fontSizeKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  getWidgetConfig 7io.despicable.chromeos.data.datastore.WidgetPreferences  iconSizeKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  lastUpdatedKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  longPreferencesKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  map 7io.despicable.chromeos.data.datastore.WidgetPreferences  preferencesDataStore 7io.despicable.chromeos.data.datastore.WidgetPreferences  provideDelegate 7io.despicable.chromeos.data.datastore.WidgetPreferences  saveWidgetConfig 7io.despicable.chromeos.data.datastore.WidgetPreferences  showHumidityKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  showWindSpeedKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  stringPreferencesKey 7io.despicable.chromeos.data.datastore.WidgetPreferences  widgetExists 7io.despicable.chromeos.data.datastore.WidgetPreferences  FontSize Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  IconSize Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  System Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  WidgetConfiguration Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  booleanPreferencesKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  cityNameKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  	dataStore Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  edit Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  fontSizeKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  iconSizeKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  lastUpdatedKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  longPreferencesKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  map Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  preferencesDataStore Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  provideDelegate Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  showHumidityKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  showWindSpeedKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  stringPreferencesKey Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  Boolean &io.despicable.chromeos.data.repository  Flow &io.despicable.chromeos.data.repository  Inject &io.despicable.chromeos.data.repository  Int &io.despicable.chromeos.data.repository  List &io.despicable.chromeos.data.repository  SampleWeatherData &io.despicable.chromeos.data.repository  	Singleton &io.despicable.chromeos.data.repository  String &io.despicable.chromeos.data.repository  
WeatherDao &io.despicable.chromeos.data.repository  WeatherData &io.despicable.chromeos.data.repository  WeatherRepository &io.despicable.chromeos.data.repository  WeatherRepositoryImpl &io.despicable.chromeos.data.repository  WidgetConfigRepository &io.despicable.chromeos.data.repository  WidgetConfigRepositoryImpl &io.despicable.chromeos.data.repository  WidgetConfiguration &io.despicable.chromeos.data.repository  WidgetPreferences &io.despicable.chromeos.data.repository  	emptyList &io.despicable.chromeos.data.repository  first &io.despicable.chromeos.data.repository  flowOf &io.despicable.chromeos.data.repository  getSampleWeatherData &io.despicable.chromeos.data.repository  map &io.despicable.chromeos.data.repository  
toDomainModel &io.despicable.chromeos.data.repository  toEntity &io.despicable.chromeos.data.repository  SampleWeatherData <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  getSampleWeatherData <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  map <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  
toDomainModel <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  toEntity <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  
weatherDao <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  	emptyList Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  first Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  flowOf Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  widgetPreferences Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Context io.despicable.chromeos.di  GetWeatherDataUseCase io.despicable.chromeos.di  Int io.despicable.chromeos.di  UpdateWidgetConfigUseCase io.despicable.chromeos.di  
WeatherDao io.despicable.chromeos.di  WeatherDatabase io.despicable.chromeos.di  WeatherRepository io.despicable.chromeos.di  WeatherRepositoryImpl io.despicable.chromeos.di  WidgetConfigRepository io.despicable.chromeos.di  WidgetConfigRepositoryImpl io.despicable.chromeos.di  WidgetConfigViewModel io.despicable.chromeos.di  WidgetPreferences io.despicable.chromeos.di  databaseModule io.despicable.chromeos.di  getDatabase io.despicable.chromeos.di  repositoryModule io.despicable.chromeos.di  
useCaseModule io.despicable.chromeos.di  viewModelModule io.despicable.chromeos.di  Boolean #io.despicable.chromeos.domain.model  Double #io.despicable.chromeos.domain.model  	Exception #io.despicable.chromeos.domain.model  Float #io.despicable.chromeos.domain.model  FontSize #io.despicable.chromeos.domain.model  GetWeatherDataUseCase #io.despicable.chromeos.domain.model  IconSize #io.despicable.chromeos.domain.model  Int #io.despicable.chromeos.domain.model  List #io.despicable.chromeos.domain.model  Long #io.despicable.chromeos.domain.model  MutableStateFlow #io.despicable.chromeos.domain.model  SampleWeatherData #io.despicable.chromeos.domain.model  Serializable #io.despicable.chromeos.domain.model  	StateFlow #io.despicable.chromeos.domain.model  String #io.despicable.chromeos.domain.model  System #io.despicable.chromeos.domain.model  UpdateWidgetConfigUseCase #io.despicable.chromeos.domain.model  	ViewModel #io.despicable.chromeos.domain.model  WeatherCondition #io.despicable.chromeos.domain.model  WeatherData #io.despicable.chromeos.domain.model  WidgetConfigUiState #io.despicable.chromeos.domain.model  WidgetConfiguration #io.despicable.chromeos.domain.model  _availableCities #io.despicable.chromeos.domain.model  _uiState #io.despicable.chromeos.domain.model  asStateFlow #io.despicable.chromeos.domain.model  distinctUntilChanged #io.despicable.chromeos.domain.model  	emptyList #io.despicable.chromeos.domain.model  firstOrNull #io.despicable.chromeos.domain.model  generateRandomWeatherData #io.despicable.chromeos.domain.model  getAvailableCities #io.despicable.chromeos.domain.model  getWeatherDataUseCase #io.despicable.chromeos.domain.model  launch #io.despicable.chromeos.domain.model  loadWeatherDataForCity #io.despicable.chromeos.domain.model  map #io.despicable.chromeos.domain.model  updateWidgetConfigUseCase #io.despicable.chromeos.domain.model  widgetId #io.despicable.chromeos.domain.model  	Companion ,io.despicable.chromeos.domain.model.FontSize  EXTRA_LARGE ,io.despicable.chromeos.domain.model.FontSize  Int ,io.despicable.chromeos.domain.model.FontSize  LARGE ,io.despicable.chromeos.domain.model.FontSize  MEDIUM ,io.despicable.chromeos.domain.model.FontSize  SMALL ,io.despicable.chromeos.domain.model.FontSize  String ,io.despicable.chromeos.domain.model.FontSize  displayName ,io.despicable.chromeos.domain.model.FontSize  name ,io.despicable.chromeos.domain.model.FontSize  sizeSp ,io.despicable.chromeos.domain.model.FontSize  valueOf ,io.despicable.chromeos.domain.model.FontSize  	Companion ,io.despicable.chromeos.domain.model.IconSize  EXTRA_LARGE ,io.despicable.chromeos.domain.model.IconSize  Int ,io.despicable.chromeos.domain.model.IconSize  LARGE ,io.despicable.chromeos.domain.model.IconSize  MEDIUM ,io.despicable.chromeos.domain.model.IconSize  SMALL ,io.despicable.chromeos.domain.model.IconSize  String ,io.despicable.chromeos.domain.model.IconSize  displayName ,io.despicable.chromeos.domain.model.IconSize  name ,io.despicable.chromeos.domain.model.IconSize  sizeDp ,io.despicable.chromeos.domain.model.IconSize  valueOf ,io.despicable.chromeos.domain.model.IconSize  CLOUDY 4io.despicable.chromeos.domain.model.WeatherCondition  	Companion 4io.despicable.chromeos.domain.model.WeatherCondition  FOGGY 4io.despicable.chromeos.domain.model.WeatherCondition  
PARTLY_CLOUDY 4io.despicable.chromeos.domain.model.WeatherCondition  RAINY 4io.despicable.chromeos.domain.model.WeatherCondition  SNOWY 4io.despicable.chromeos.domain.model.WeatherCondition  STORMY 4io.despicable.chromeos.domain.model.WeatherCondition  SUNNY 4io.despicable.chromeos.domain.model.WeatherCondition  String 4io.despicable.chromeos.domain.model.WeatherCondition  name 4io.despicable.chromeos.domain.model.WeatherCondition  valueOf 4io.despicable.chromeos.domain.model.WeatherCondition  values 4io.despicable.chromeos.domain.model.WeatherCondition  Double /io.despicable.chromeos.domain.model.WeatherData  Int /io.despicable.chromeos.domain.model.WeatherData  Long /io.despicable.chromeos.domain.model.WeatherData  String /io.despicable.chromeos.domain.model.WeatherData  System /io.despicable.chromeos.domain.model.WeatherData  WeatherCondition /io.despicable.chromeos.domain.model.WeatherData  
WeatherEntity /io.despicable.chromeos.domain.model.WeatherData  cityName /io.despicable.chromeos.domain.model.WeatherData  humidity /io.despicable.chromeos.domain.model.WeatherData  id /io.despicable.chromeos.domain.model.WeatherData  lastUpdated /io.despicable.chromeos.domain.model.WeatherData  temperature /io.despicable.chromeos.domain.model.WeatherData  toEntity /io.despicable.chromeos.domain.model.WeatherData  weatherCondition /io.despicable.chromeos.domain.model.WeatherData  	windSpeed /io.despicable.chromeos.domain.model.WeatherData  System 9io.despicable.chromeos.domain.model.WeatherData.Companion  Boolean 7io.despicable.chromeos.domain.model.WidgetConfiguration  FontSize 7io.despicable.chromeos.domain.model.WidgetConfiguration  IconSize 7io.despicable.chromeos.domain.model.WidgetConfiguration  Int 7io.despicable.chromeos.domain.model.WidgetConfiguration  Long 7io.despicable.chromeos.domain.model.WidgetConfiguration  String 7io.despicable.chromeos.domain.model.WidgetConfiguration  System 7io.despicable.chromeos.domain.model.WidgetConfiguration  cityName 7io.despicable.chromeos.domain.model.WidgetConfiguration  fontSize 7io.despicable.chromeos.domain.model.WidgetConfiguration  iconSize 7io.despicable.chromeos.domain.model.WidgetConfiguration  lastUpdated 7io.despicable.chromeos.domain.model.WidgetConfiguration  showHumidity 7io.despicable.chromeos.domain.model.WidgetConfiguration  
showWindSpeed 7io.despicable.chromeos.domain.model.WidgetConfiguration  widgetId 7io.despicable.chromeos.domain.model.WidgetConfiguration  FontSize Aio.despicable.chromeos.domain.model.WidgetConfiguration.Companion  IconSize Aio.despicable.chromeos.domain.model.WidgetConfiguration.Companion  System Aio.despicable.chromeos.domain.model.WidgetConfiguration.Companion  Boolean (io.despicable.chromeos.domain.repository  Flow (io.despicable.chromeos.domain.repository  Int (io.despicable.chromeos.domain.repository  List (io.despicable.chromeos.domain.repository  String (io.despicable.chromeos.domain.repository  WeatherData (io.despicable.chromeos.domain.repository  WeatherRepository (io.despicable.chromeos.domain.repository  WidgetConfigRepository (io.despicable.chromeos.domain.repository  WidgetConfiguration (io.despicable.chromeos.domain.repository  getAllCities :io.despicable.chromeos.domain.repository.WeatherRepository  getAllWeatherData :io.despicable.chromeos.domain.repository.WeatherRepository  getWeatherByCity :io.despicable.chromeos.domain.repository.WeatherRepository  refreshWeatherData :io.despicable.chromeos.domain.repository.WeatherRepository  deleteWidgetConfig ?io.despicable.chromeos.domain.repository.WidgetConfigRepository  getAllWidgetConfigs ?io.despicable.chromeos.domain.repository.WidgetConfigRepository  getWidgetConfig ?io.despicable.chromeos.domain.repository.WidgetConfigRepository  saveWidgetConfig ?io.despicable.chromeos.domain.repository.WidgetConfigRepository  widgetExists ?io.despicable.chromeos.domain.repository.WidgetConfigRepository  Boolean %io.despicable.chromeos.domain.usecase  Flow %io.despicable.chromeos.domain.usecase  GetWeatherDataUseCase %io.despicable.chromeos.domain.usecase  Inject %io.despicable.chromeos.domain.usecase  Int %io.despicable.chromeos.domain.usecase  List %io.despicable.chromeos.domain.usecase  String %io.despicable.chromeos.domain.usecase  UpdateWidgetConfigUseCase %io.despicable.chromeos.domain.usecase  WeatherData %io.despicable.chromeos.domain.usecase  WeatherRepository %io.despicable.chromeos.domain.usecase  WidgetConfigRepository %io.despicable.chromeos.domain.usecase  WidgetConfiguration %io.despicable.chromeos.domain.usecase  getWeatherByCity ;io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase  refreshWeatherData ;io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase  weatherRepository ;io.despicable.chromeos.domain.usecase.GetWeatherDataUseCase  getWidgetConfig ?io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase  saveWidgetConfig ?io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase  widgetConfigRepository ?io.despicable.chromeos.domain.usecase.UpdateWidgetConfigUseCase  AdditionalOptionsSection -io.despicable.chromeos.presentation.ui.config  	Alignment -io.despicable.chromeos.presentation.ui.config  AppWidgetManager -io.despicable.chromeos.presentation.ui.config  Arrangement -io.despicable.chromeos.presentation.ui.config  Boolean -io.despicable.chromeos.presentation.ui.config  Box -io.despicable.chromeos.presentation.ui.config  Bundle -io.despicable.chromeos.presentation.ui.config  Button -io.despicable.chromeos.presentation.ui.config  Card -io.despicable.chromeos.presentation.ui.config  CardDefaults -io.despicable.chromeos.presentation.ui.config  
ChromeOSTheme -io.despicable.chromeos.presentation.ui.config  CircularProgressIndicator -io.despicable.chromeos.presentation.ui.config  CitySelectionSection -io.despicable.chromeos.presentation.ui.config  Color -io.despicable.chromeos.presentation.ui.config  Column -io.despicable.chromeos.presentation.ui.config  ComponentActivity -io.despicable.chromeos.presentation.ui.config  
Composable -io.despicable.chromeos.presentation.ui.config  	Exception -io.despicable.chromeos.presentation.ui.config  ExperimentalMaterial3Api -io.despicable.chromeos.presentation.ui.config  
FilterChip -io.despicable.chromeos.presentation.ui.config  Float -io.despicable.chromeos.presentation.ui.config  FontSize -io.despicable.chromeos.presentation.ui.config  FontSizeSliderSection -io.despicable.chromeos.presentation.ui.config  
FontWeight -io.despicable.chromeos.presentation.ui.config  GetWeatherDataUseCase -io.despicable.chromeos.presentation.ui.config  IconSize -io.despicable.chromeos.presentation.ui.config  IconSizeSliderSection -io.despicable.chromeos.presentation.ui.config  Intent -io.despicable.chromeos.presentation.ui.config  
LazyColumn -io.despicable.chromeos.presentation.ui.config  LazyRow -io.despicable.chromeos.presentation.ui.config  List -io.despicable.chromeos.presentation.ui.config  
MaterialTheme -io.despicable.chromeos.presentation.ui.config  Modifier -io.despicable.chromeos.presentation.ui.config  OptIn -io.despicable.chromeos.presentation.ui.config  PreviewSection -io.despicable.chromeos.presentation.ui.config  RESULT_CANCELED -io.despicable.chromeos.presentation.ui.config  	RESULT_OK -io.despicable.chromeos.presentation.ui.config  Row -io.despicable.chromeos.presentation.ui.config  Scaffold -io.despicable.chromeos.presentation.ui.config  Slider -io.despicable.chromeos.presentation.ui.config  Spacer -io.despicable.chromeos.presentation.ui.config  String -io.despicable.chromeos.presentation.ui.config  Switch -io.despicable.chromeos.presentation.ui.config  Text -io.despicable.chromeos.presentation.ui.config  	TextAlign -io.despicable.chromeos.presentation.ui.config  Unit -io.despicable.chromeos.presentation.ui.config  UpdateWidgetConfigUseCase -io.despicable.chromeos.presentation.ui.config  WeatherCondition -io.despicable.chromeos.presentation.ui.config  WeatherData -io.despicable.chromeos.presentation.ui.config  
WeatherWidget -io.despicable.chromeos.presentation.ui.config  WidgetConfigActivity -io.despicable.chromeos.presentation.ui.config  WidgetConfigScreen -io.despicable.chromeos.presentation.ui.config  WidgetConfigViewModel -io.despicable.chromeos.presentation.ui.config  WidgetConfiguration -io.despicable.chromeos.presentation.ui.config  WidgetPreviewComponent -io.despicable.chromeos.presentation.ui.config  appWidgetId -io.despicable.chromeos.presentation.ui.config  apply -io.despicable.chromeos.presentation.ui.config  
cardColors -io.despicable.chromeos.presentation.ui.config  
cardElevation -io.despicable.chromeos.presentation.ui.config  collectAsState -io.despicable.chromeos.presentation.ui.config  fillMaxSize -io.despicable.chromeos.presentation.ui.config  fillMaxWidth -io.despicable.chromeos.presentation.ui.config  finish -io.despicable.chromeos.presentation.ui.config  format -io.despicable.chromeos.presentation.ui.config  getValue -io.despicable.chromeos.presentation.ui.config  getWeatherEmoji -io.despicable.chromeos.presentation.ui.config  height -io.despicable.chromeos.presentation.ui.config  io -io.despicable.chromeos.presentation.ui.config  launch -io.despicable.chromeos.presentation.ui.config  padding -io.despicable.chromeos.presentation.ui.config  parametersOf -io.despicable.chromeos.presentation.ui.config  provideDelegate -io.despicable.chromeos.presentation.ui.config  rangeTo -io.despicable.chromeos.presentation.ui.config  	setResult -io.despicable.chromeos.presentation.ui.config  size -io.despicable.chromeos.presentation.ui.config  spacedBy -io.despicable.chromeos.presentation.ui.config  	updateAll -io.despicable.chromeos.presentation.ui.config  width -io.despicable.chromeos.presentation.ui.config  AppWidgetManager Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  
ChromeOSTheme Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  Intent Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  Modifier Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  RESULT_CANCELED Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  	RESULT_OK Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  Scaffold Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  
WeatherWidget Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  WidgetConfigScreen Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  appWidgetId Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  apply Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  enableEdgeToEdge Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  fillMaxSize Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  finish Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  getValue Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  inject Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  intent Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  launch Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  lifecycleScope Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  padding Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  parametersOf Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  provideDelegate Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  saveConfigurationAndFinish Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  
setContent Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  	setResult Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  	updateAll Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  
despicable 0io.despicable.chromeos.presentation.ui.config.io  chromeos ;io.despicable.chromeos.presentation.ui.config.io.despicable  domain Dio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos  model Kio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain  WeatherData Qio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain.model  WidgetConfiguration Qio.despicable.chromeos.presentation.ui.config.io.despicable.chromeos.domain.model  ACTION_UPDATE_WEATHER -io.despicable.chromeos.presentation.ui.widget  	Alignment -io.despicable.chromeos.presentation.ui.widget  AppWidgetManager -io.despicable.chromeos.presentation.ui.widget  Box -io.despicable.chromeos.presentation.ui.widget  Color -io.despicable.chromeos.presentation.ui.widget  
ColorProvider -io.despicable.chromeos.presentation.ui.widget  Column -io.despicable.chromeos.presentation.ui.widget  
Composable -io.despicable.chromeos.presentation.ui.widget  Context -io.despicable.chromeos.presentation.ui.widget  	Exception -io.despicable.chromeos.presentation.ui.widget  ExistingWorkPolicy -io.despicable.chromeos.presentation.ui.widget  
FontWeight -io.despicable.chromeos.presentation.ui.widget  GlanceAppWidget -io.despicable.chromeos.presentation.ui.widget  GlanceAppWidgetReceiver -io.despicable.chromeos.presentation.ui.widget  GlanceId -io.despicable.chromeos.presentation.ui.widget  GlanceModifier -io.despicable.chromeos.presentation.ui.widget  GlanceTheme -io.despicable.chromeos.presentation.ui.widget  Int -io.despicable.chromeos.presentation.ui.widget  IntArray -io.despicable.chromeos.presentation.ui.widget  Intent -io.despicable.chromeos.presentation.ui.widget  OneTimeWorkRequestBuilder -io.despicable.chromeos.presentation.ui.widget  Row -io.despicable.chromeos.presentation.ui.widget  SampleWeatherData -io.despicable.chromeos.presentation.ui.widget  Spacer -io.despicable.chromeos.presentation.ui.widget  String -io.despicable.chromeos.presentation.ui.widget  Text -io.despicable.chromeos.presentation.ui.widget  	TextStyle -io.despicable.chromeos.presentation.ui.widget  WEATHER_UPDATE_WORK_NAME -io.despicable.chromeos.presentation.ui.widget  WeatherCondition -io.despicable.chromeos.presentation.ui.widget  WeatherData -io.despicable.chromeos.presentation.ui.widget  WeatherDatabase -io.despicable.chromeos.presentation.ui.widget  WeatherUpdateWorker -io.despicable.chromeos.presentation.ui.widget  
WeatherWidget -io.despicable.chromeos.presentation.ui.widget  WeatherWidgetProvider -io.despicable.chromeos.presentation.ui.widget  WidgetConfiguration -io.despicable.chromeos.presentation.ui.widget  WidgetPreferences -io.despicable.chromeos.presentation.ui.widget  WorkManager -io.despicable.chromeos.presentation.ui.widget  
background -io.despicable.chromeos.presentation.ui.widget  fillMaxSize -io.despicable.chromeos.presentation.ui.widget  fillMaxWidth -io.despicable.chromeos.presentation.ui.widget  first -io.despicable.chromeos.presentation.ui.widget  format -io.despicable.chromeos.presentation.ui.widget  generateRandomWeatherData -io.despicable.chromeos.presentation.ui.widget  getDatabase -io.despicable.chromeos.presentation.ui.widget  getInstance -io.despicable.chromeos.presentation.ui.widget  getWeatherEmoji -io.despicable.chromeos.presentation.ui.widget  height -io.despicable.chromeos.presentation.ui.widget  padding -io.despicable.chromeos.presentation.ui.widget  
toDomainModel -io.despicable.chromeos.presentation.ui.widget  width -io.despicable.chromeos.presentation.ui.widget  	Alignment ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Box ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Color ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  
ColorProvider ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Column ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  
FontWeight ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  GlanceModifier ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  GlanceTheme ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Row ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  SampleWeatherData ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Spacer ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  String ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  Text ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  	TextStyle ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  WeatherCondition ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  WeatherDatabase ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  WeatherWidgetContent ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  WidgetPreferences ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  
background ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  dp ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  fillMaxSize ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  fillMaxWidth ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  first ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  format ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  generateRandomWeatherData ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  getDatabase ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  getWeatherData ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  getWeatherEmoji ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  getWidgetConfiguration ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  getWidgetId ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  height ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  padding ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  provideContent ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  sp ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  
toDomainModel ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  	updateAll ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  width ;io.despicable.chromeos.presentation.ui.widget.WeatherWidget  ACTION_UPDATE_WEATHER Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  AppWidgetManager Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  Context Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  ExistingWorkPolicy Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  GlanceAppWidget Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  IntArray Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  Intent Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  OneTimeWorkRequestBuilder Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  WEATHER_UPDATE_WORK_NAME Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  WeatherUpdateWorker Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  
WeatherWidget Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  WorkManager Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  getInstance Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  scheduleWeatherUpdate Cio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider  ACTION_UPDATE_WEATHER Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  ExistingWorkPolicy Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  OneTimeWorkRequestBuilder Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  WEATHER_UPDATE_WORK_NAME Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  
WeatherWidget Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  WorkManager Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  getInstance Mio.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider.Companion  Boolean -io.despicable.chromeos.presentation.viewmodel  	Exception -io.despicable.chromeos.presentation.viewmodel  Float -io.despicable.chromeos.presentation.viewmodel  FontSize -io.despicable.chromeos.presentation.viewmodel  GetWeatherDataUseCase -io.despicable.chromeos.presentation.viewmodel  IconSize -io.despicable.chromeos.presentation.viewmodel  Int -io.despicable.chromeos.presentation.viewmodel  List -io.despicable.chromeos.presentation.viewmodel  MutableStateFlow -io.despicable.chromeos.presentation.viewmodel  SampleWeatherData -io.despicable.chromeos.presentation.viewmodel  	StateFlow -io.despicable.chromeos.presentation.viewmodel  String -io.despicable.chromeos.presentation.viewmodel  UpdateWidgetConfigUseCase -io.despicable.chromeos.presentation.viewmodel  	ViewModel -io.despicable.chromeos.presentation.viewmodel  WeatherData -io.despicable.chromeos.presentation.viewmodel  WidgetConfigUiState -io.despicable.chromeos.presentation.viewmodel  WidgetConfigViewModel -io.despicable.chromeos.presentation.viewmodel  WidgetConfiguration -io.despicable.chromeos.presentation.viewmodel  _availableCities -io.despicable.chromeos.presentation.viewmodel  _uiState -io.despicable.chromeos.presentation.viewmodel  asStateFlow -io.despicable.chromeos.presentation.viewmodel  distinctUntilChanged -io.despicable.chromeos.presentation.viewmodel  	emptyList -io.despicable.chromeos.presentation.viewmodel  firstOrNull -io.despicable.chromeos.presentation.viewmodel  generateRandomWeatherData -io.despicable.chromeos.presentation.viewmodel  getAvailableCities -io.despicable.chromeos.presentation.viewmodel  getWeatherDataUseCase -io.despicable.chromeos.presentation.viewmodel  launch -io.despicable.chromeos.presentation.viewmodel  loadWeatherDataForCity -io.despicable.chromeos.presentation.viewmodel  map -io.despicable.chromeos.presentation.viewmodel  updateWidgetConfigUseCase -io.despicable.chromeos.presentation.viewmodel  widgetId -io.despicable.chromeos.presentation.viewmodel  copy Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  	isLoading Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  selectedCity Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  selectedFontSize Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  selectedIconSize Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  showHumidity Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  
showWindSpeed Aio.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState  FontSize Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  IconSize Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  MutableStateFlow Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  SampleWeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WidgetConfigUiState Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WidgetConfiguration Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  _availableCities Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  _previewWeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  _uiState Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  asStateFlow Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  availableCities Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  distinctUntilChanged Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  	emptyList Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  firstOrNull Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  generateRandomWeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  getAvailableCities Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  getCurrentConfiguration Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  getFontSizeSliderValue Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  getIconSizeSliderValue Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  getWeatherDataUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  launch Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  loadInitialData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  loadWeatherDataForCity Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  map Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  observeCitySelection Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  onCitySelected Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  onFontSizeSliderChanged Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  onIconSizeSliderChanged Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  onShowHumidityToggled Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  onShowWindSpeedToggled Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  previewWeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  saveConfiguration Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  uiState Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  updateWidgetConfigUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  viewModelScope Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  widgetId Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Context *io.despicable.chromeos.presentation.worker  CoroutineWorker *io.despicable.chromeos.presentation.worker  	Exception *io.despicable.chromeos.presentation.worker  GetWeatherDataUseCase *io.despicable.chromeos.presentation.worker  
KoinComponent *io.despicable.chromeos.presentation.worker  Result *io.despicable.chromeos.presentation.worker  WeatherUpdateWorker *io.despicable.chromeos.presentation.worker  
WeatherWidget *io.despicable.chromeos.presentation.worker  WorkerParameters *io.despicable.chromeos.presentation.worker  getValue *io.despicable.chromeos.presentation.worker  provideDelegate *io.despicable.chromeos.presentation.worker  	updateAll *io.despicable.chromeos.presentation.worker  Result >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  
WeatherWidget >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  applicationContext >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  getValue >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  getWeatherDataUseCase >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  inject >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  provideDelegate >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  	updateAll >io.despicable.chromeos.presentation.worker.WeatherUpdateWorker  Boolean io.despicable.chromeos.ui.theme  Build io.despicable.chromeos.ui.theme  
ChromeOSTheme io.despicable.chromeos.ui.theme  
Composable io.despicable.chromeos.ui.theme  DarkColorScheme io.despicable.chromeos.ui.theme  
FontFamily io.despicable.chromeos.ui.theme  
FontWeight io.despicable.chromeos.ui.theme  LightColorScheme io.despicable.chromeos.ui.theme  Pink40 io.despicable.chromeos.ui.theme  Pink80 io.despicable.chromeos.ui.theme  Purple40 io.despicable.chromeos.ui.theme  Purple80 io.despicable.chromeos.ui.theme  PurpleGrey40 io.despicable.chromeos.ui.theme  PurpleGrey80 io.despicable.chromeos.ui.theme  
Typography io.despicable.chromeos.ui.theme  Unit io.despicable.chromeos.ui.theme  List io.despicable.chromeos.util  Random io.despicable.chromeos.util  SampleWeatherData io.despicable.chromeos.util  String io.despicable.chromeos.util  System io.despicable.chromeos.util  WeatherCondition io.despicable.chromeos.util  WeatherData io.despicable.chromeos.util  find io.despicable.chromeos.util  listOf io.despicable.chromeos.util  
mapIndexed io.despicable.chromeos.util  
nextDouble io.despicable.chromeos.util  nextInt io.despicable.chromeos.util  nextLong io.despicable.chromeos.util  random io.despicable.chromeos.util  Random -io.despicable.chromeos.util.SampleWeatherData  System -io.despicable.chromeos.util.SampleWeatherData  WeatherCondition -io.despicable.chromeos.util.SampleWeatherData  WeatherData -io.despicable.chromeos.util.SampleWeatherData  cities -io.despicable.chromeos.util.SampleWeatherData  find -io.despicable.chromeos.util.SampleWeatherData  generateRandomWeatherData -io.despicable.chromeos.util.SampleWeatherData  getAvailableCities -io.despicable.chromeos.util.SampleWeatherData  getSampleWeatherData -io.despicable.chromeos.util.SampleWeatherData  listOf -io.despicable.chromeos.util.SampleWeatherData  
mapIndexed -io.despicable.chromeos.util.SampleWeatherData  
nextDouble -io.despicable.chromeos.util.SampleWeatherData  nextInt -io.despicable.chromeos.util.SampleWeatherData  nextLong -io.despicable.chromeos.util.SampleWeatherData  random -io.despicable.chromeos.util.SampleWeatherData  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  currentTimeMillis java.lang.System  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Array kotlin  Double kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Lazy kotlin  Long kotlin  Nothing kotlin  OptIn kotlin  Result kotlin  String kotlin  Suppress kotlin  Unit kotlin  apply kotlin  arrayOf kotlin  getValue kotlin  lazy kotlin  map kotlin  synchronized kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  random kotlin.Array  not kotlin.Boolean  sp 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  rangeTo kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  sp 
kotlin.Int  toLong 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  value kotlin.Lazy  minus kotlin.Long  plus kotlin.Long  toInt kotlin.Long  	Companion 
kotlin.String  format 
kotlin.String  hashCode 
kotlin.String  plus 
kotlin.String  
trimMargin 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  	emptyList kotlin.collections  find kotlin.collections  firstOrNull kotlin.collections  getValue kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  random kotlin.collections  find kotlin.collections.List  firstOrNull kotlin.collections.List  map kotlin.collections.List  
mapIndexed kotlin.collections.List  add kotlin.collections.MutableList  put kotlin.collections.MutableMap  SuspendFunction1 kotlin.coroutines  Volatile 
kotlin.jvm  java 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  Random 
kotlin.random  Default kotlin.random.Random  
nextDouble kotlin.random.Random  nextInt kotlin.random.Random  nextLong kotlin.random.Random  
nextDouble kotlin.random.Random.Default  nextInt kotlin.random.Random.Default  nextLong kotlin.random.Random.Default  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  firstOrNull 
kotlin.ranges  random 
kotlin.ranges  rangeTo 
kotlin.ranges  KClass kotlin.reflect  
KFunction1 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  
KProperty2 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  find kotlin.sequences  firstOrNull kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  find kotlin.text  firstOrNull kotlin.text  format kotlin.text  map kotlin.text  
mapIndexed kotlin.text  random kotlin.text  
trimMargin kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  AppWidgetManager !kotlinx.coroutines.CoroutineScope  FontSize !kotlinx.coroutines.CoroutineScope  IconSize !kotlinx.coroutines.CoroutineScope  Intent !kotlinx.coroutines.CoroutineScope  	RESULT_OK !kotlinx.coroutines.CoroutineScope  SampleWeatherData !kotlinx.coroutines.CoroutineScope  
WeatherWidget !kotlinx.coroutines.CoroutineScope  WidgetConfiguration !kotlinx.coroutines.CoroutineScope  _availableCities !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  appWidgetId !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  distinctUntilChanged !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  getAvailableCities !kotlinx.coroutines.CoroutineScope  getWeatherDataUseCase !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadWeatherDataForCity !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  	setResult !kotlinx.coroutines.CoroutineScope  	updateAll !kotlinx.coroutines.CoroutineScope  updateWidgetConfigUseCase !kotlinx.coroutines.CoroutineScope  widgetId !kotlinx.coroutines.CoroutineScope  Boolean kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  Float kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  FontSize kotlinx.coroutines.flow  GetWeatherDataUseCase kotlinx.coroutines.flow  IconSize kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SampleWeatherData kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  String kotlinx.coroutines.flow  UpdateWidgetConfigUseCase kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  WeatherData kotlinx.coroutines.flow  WidgetConfigUiState kotlinx.coroutines.flow  WidgetConfiguration kotlinx.coroutines.flow  _availableCities kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  distinctUntilChanged kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  first kotlinx.coroutines.flow  firstOrNull kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  generateRandomWeatherData kotlinx.coroutines.flow  getAvailableCities kotlinx.coroutines.flow  getWeatherDataUseCase kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  loadWeatherDataForCity kotlinx.coroutines.flow  map kotlinx.coroutines.flow  updateWidgetConfigUseCase kotlinx.coroutines.flow  widgetId kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  distinctUntilChanged kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  map (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  Serializable kotlinx.serialization  inject org.koin.android.ext.android  androidContext org.koin.android.ext.koin  
androidLogger org.koin.android.ext.koin  KoinApplication 
org.koin.core  Level org.koin.core.KoinApplication  androidContext org.koin.core.KoinApplication  
androidLogger org.koin.core.KoinApplication  databaseModule org.koin.core.KoinApplication  modules org.koin.core.KoinApplication  repositoryModule org.koin.core.KoinApplication  
useCaseModule org.koin.core.KoinApplication  viewModelModule org.koin.core.KoinApplication  
KoinComponent org.koin.core.component  inject org.koin.core.component  	startKoin org.koin.core.context  KoinDefinition org.koin.core.definition  Level org.koin.core.logger  ERROR org.koin.core.logger.Level  Module org.koin.core.module  GetWeatherDataUseCase org.koin.core.module.Module  UpdateWidgetConfigUseCase org.koin.core.module.Module  WeatherDatabase org.koin.core.module.Module  WeatherRepositoryImpl org.koin.core.module.Module  WidgetConfigRepositoryImpl org.koin.core.module.Module  WidgetConfigViewModel org.koin.core.module.Module  WidgetPreferences org.koin.core.module.Module  factory org.koin.core.module.Module  getDatabase org.koin.core.module.Module  single org.koin.core.module.Module  ParametersHolder org.koin.core.parameter  parametersOf org.koin.core.parameter  
component1 (org.koin.core.parameter.ParametersHolder  Scope org.koin.core.scope  GetWeatherDataUseCase org.koin.core.scope.Scope  UpdateWidgetConfigUseCase org.koin.core.scope.Scope  WeatherDatabase org.koin.core.scope.Scope  WeatherRepositoryImpl org.koin.core.scope.Scope  WidgetConfigRepositoryImpl org.koin.core.scope.Scope  WidgetConfigViewModel org.koin.core.scope.Scope  WidgetPreferences org.koin.core.scope.Scope  get org.koin.core.scope.Scope  getDatabase org.koin.core.scope.Scope  module org.koin.dsl  Log android.app.Activity  TAG android.app.Activity  Log android.content.Context  TAG android.content.Context  Log android.content.ContextWrapper  TAG android.content.ContextWrapper  Log android.util  d android.util.Log  e android.util.Log  Log  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Log #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Log -androidx.activity.ComponentActivity.Companion  TAG -androidx.activity.ComponentActivity.Companion  Log "androidx.compose.foundation.layout  Log .androidx.compose.foundation.layout.ColumnScope  Log +androidx.compose.foundation.layout.RowScope  Log .androidx.compose.foundation.lazy.LazyItemScope  Log .androidx.compose.foundation.lazy.LazyListScope  Log androidx.compose.material3  Log androidx.compose.runtime  Log #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Log #androidx.datastore.preferences.core  TAG #androidx.datastore.preferences.core  Log %io.despicable.chromeos.data.datastore  TAG %io.despicable.chromeos.data.datastore  Log 7io.despicable.chromeos.data.datastore.WidgetPreferences  TAG 7io.despicable.chromeos.data.datastore.WidgetPreferences  Log Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  TAG Aio.despicable.chromeos.data.datastore.WidgetPreferences.Companion  Log &io.despicable.chromeos.data.repository  TAG &io.despicable.chromeos.data.repository  Flow <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  Inject <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  List <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  Log <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  String <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  TAG <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  
WeatherDao <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  WeatherData <io.despicable.chromeos.data.repository.WeatherRepositoryImpl  Log Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  SampleWeatherData Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  TAG Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  getSampleWeatherData Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  map Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  
toDomainModel Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  toEntity Fio.despicable.chromeos.data.repository.WeatherRepositoryImpl.Companion  Boolean Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Flow Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Inject Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Int Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  List Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Log Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  TAG Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  WidgetConfiguration Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  WidgetPreferences Aio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl  Log Kio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl.Companion  TAG Kio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl.Companion  	emptyList Kio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl.Companion  first Kio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl.Companion  flowOf Kio.despicable.chromeos.data.repository.WidgetConfigRepositoryImpl.Companion  Log #io.despicable.chromeos.domain.model  TAG #io.despicable.chromeos.domain.model  Log -io.despicable.chromeos.presentation.ui.config  TAG -io.despicable.chromeos.presentation.ui.config  Bundle Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  	Exception Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  GetWeatherDataUseCase Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  Log Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  TAG Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  UpdateWidgetConfigUseCase Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  WidgetConfigViewModel Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  AppWidgetManager Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  
ChromeOSTheme Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  Intent Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  Log Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  Modifier Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  RESULT_CANCELED Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  	RESULT_OK Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  Scaffold Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  TAG Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  
WeatherWidget Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  WidgetConfigScreen Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  appWidgetId Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  apply Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  enableEdgeToEdge Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  fillMaxSize Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  finish Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  getValue Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  inject Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  launch Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  lifecycleScope Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  padding Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  parametersOf Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  provideDelegate Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  
setContent Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  	setResult Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  	updateAll Lio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity.Companion  Log -io.despicable.chromeos.presentation.viewmodel  TAG -io.despicable.chromeos.presentation.viewmodel  Boolean Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  	Exception Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Float Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  GetWeatherDataUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Int Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  List Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  Log Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  	StateFlow Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  String Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  TAG Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  UpdateWidgetConfigUseCase Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  WeatherData Cio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel  FontSize Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  IconSize Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  Log Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  MutableStateFlow Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  SampleWeatherData Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  TAG Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  WidgetConfigUiState Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  WidgetConfiguration Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  _availableCities Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  _uiState Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  asStateFlow Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  distinctUntilChanged Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  	emptyList Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  firstOrNull Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  generateRandomWeatherData Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  getAvailableCities Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  getWeatherDataUseCase Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  launch Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  loadWeatherDataForCity Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  map Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  updateWidgetConfigUseCase Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  viewModelScope Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  widgetId Mio.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel.Companion  size kotlin.collections.List  Log !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Log kotlinx.coroutines.flow  TAG kotlinx.coroutines.flow  	Companion Bio.despicable.chromeos.presentation.ui.config.WidgetConfigActivity  remember "androidx.compose.foundation.layout  remember .androidx.compose.foundation.layout.ColumnScope  remember .androidx.compose.foundation.lazy.LazyItemScope  remember .androidx.compose.foundation.lazy.LazyListScope  remember androidx.compose.material3  remember androidx.compose.runtime  remember -io.despicable.chromeos.presentation.ui.config                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           