<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageButton
        android:id="@+id/back_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:background="@null"
        android:padding="8dp"
        android:src="@drawable/ic_back"
        app:layout_constraintBottom_toTopOf="@id/barrier_nav"
        app:layout_constraintEnd_toStartOf="@id/crop_button"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="#FFFFFF"
        tools:ignore="ContentDescription" />

    <ImageButton
        android:id="@+id/crop_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:background="@null"
        android:padding="8dp"
        android:src="@drawable/ic_check"
        app:layout_constraintBottom_toTopOf="@id/barrier_nav"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/back_button"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="#FFFFFF"
        tools:ignore="ContentDescription" />

    <ProgressBar
        android:id="@+id/progress_indicator"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:indeterminateTint="#FFFFFF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/crop_button"
        app:layout_constraintEnd_toEndOf="@id/crop_button"
        app:layout_constraintStart_toStartOf="@id/crop_button"
        app:layout_constraintTop_toTopOf="@id/crop_button" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_nav"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="back_button, crop_button" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barrier_nav" />

</androidx.constraintlayout.widget.ConstraintLayout>
