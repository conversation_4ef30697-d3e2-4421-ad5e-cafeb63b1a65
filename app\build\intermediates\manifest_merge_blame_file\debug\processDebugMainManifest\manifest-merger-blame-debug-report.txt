1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.despicable.chromeos.debug"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.WAKE_LOCK" />
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
15
16    <permission
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
17        android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:5:5-51:19
23        android:name="io.despicable.chromeos.WeatherApplication"
23-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:6:9-43
24        android:allowBackup="true"
24-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:7:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:8:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:9:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:10:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:11:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:12:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:13:9-35
34        android:theme="@style/Theme.ChromeOS" >
34-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:14:9-46
35        <activity
35-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:16:9-26:20
36            android:name="io.despicable.chromeos.MainActivity"
36-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:17:13-41
37            android:exported="true"
37-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:18:13-36
38            android:label="@string/app_name"
38-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:19:13-45
39            android:theme="@style/Theme.ChromeOS" >
39-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:20:13-50
40            <intent-filter>
40-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:21:13-25:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:17-69
41-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:17-77
43-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:27-74
44            </intent-filter>
45        </activity>
46
47        <!-- Widget Configuration Activity -->
48        <activity
48-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:29:9-36:20
49            android:name="io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity"
49-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:30:13-72
50            android:exported="false"
50-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:31:13-37
51            android:theme="@style/Theme.ChromeOS" >
51-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:32:13-50
52            <intent-filter>
52-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:33:13-35:29
53                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
53-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:17-87
53-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:25-84
54            </intent-filter>
55        </activity>
56
57        <!-- Weather Widget Provider -->
58        <receiver
58-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:39:9-50:20
59            android:name="io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider"
59-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:40:13-73
60            android:exported="true" >
60-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:41:13-36
61            <intent-filter>
61-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:42:13-46:29
62                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
62-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:17-84
62-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:25-81
63                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
63-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:17-85
63-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:25-82
64                <action android:name="io.despicable.chromeos.ACTION_UPDATE_WEATHER" />
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:17-87
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:25-84
65            </intent-filter>
66
67            <meta-data
67-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:47:13-49:63
68                android:name="android.appwidget.provider"
68-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:48:17-58
69                android:resource="@xml/weather_widget_info" />
69-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:49:17-60
70        </receiver>
71
72        <activity
72-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
73            android:name="androidx.glance.appwidget.action.ActionTrampolineActivity"
73-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
74            android:enabled="true"
74-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
75            android:excludeFromRecents="true"
75-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
76            android:exported="false"
76-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
77            android:theme="@android:style/Theme.NoDisplay" />
77-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
78        <activity
78-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
79            android:name="androidx.glance.appwidget.action.InvisibleActionTrampolineActivity"
79-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
80            android:enabled="true"
80-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
81            android:exported="false"
81-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
82            android:launchMode="singleInstance"
82-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
83            android:noHistory="true"
83-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
84            android:taskAffinity="androidx.glance.appwidget.ListAdapterCallbackTrampoline"
84-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
85            android:theme="@style/Widget.Glance.AppWidget.CallbackTrampoline" />
85-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
86
87        <receiver
87-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
88            android:name="androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver"
88-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
89            android:enabled="true"
89-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
90            android:exported="false" />
90-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
91        <receiver
91-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
92            android:name="androidx.glance.appwidget.UnmanagedSessionReceiver"
92-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
93            android:enabled="true"
93-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
94            android:exported="false" />
94-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
95        <receiver
95-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
96            android:name="androidx.glance.appwidget.MyPackageReplacedReceiver"
96-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
97            android:enabled="true"
97-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
98            android:exported="false" >
98-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
99            <intent-filter>
99-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
100                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
100-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
100-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
101            </intent-filter>
102        </receiver>
103
104        <service
104-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
105            android:name="androidx.glance.appwidget.GlanceRemoteViewsService"
105-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
106            android:exported="true"
106-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
107            android:permission="android.permission.BIND_REMOTEVIEWS" />
107-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
108
109        <activity
109-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
110            android:name="androidx.compose.ui.tooling.PreviewActivity"
110-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
111            android:exported="true" />
111-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
112
113        <provider
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
115            android:authorities="io.despicable.chromeos.debug.androidx-startup"
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
116            android:exported="false" >
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
117            <meta-data
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
118                android:name="androidx.work.WorkManagerInitializer"
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
119                android:value="androidx.startup" />
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
120            <meta-data
120-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.emoji2.text.EmojiCompatInitializer"
121-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
122                android:value="androidx.startup" />
122-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
123            <meta-data
123-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
124-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
125                android:value="androidx.startup" />
125-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
128                android:value="androidx.startup" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
129        </provider>
130
131        <service
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
132            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
134            android:enabled="@bool/enable_system_alarm_service_default"
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
135            android:exported="false" />
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
136        <service
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
137            android:name="androidx.work.impl.background.systemjob.SystemJobService"
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
139            android:enabled="@bool/enable_system_job_service_default"
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
140            android:exported="true"
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
141            android:permission="android.permission.BIND_JOB_SERVICE" />
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
142        <service
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
143            android:name="androidx.work.impl.foreground.SystemForegroundService"
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
145            android:enabled="@bool/enable_system_foreground_service_default"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
146            android:exported="false" />
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
147
148        <receiver
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
149            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
151            android:enabled="true"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
152            android:exported="false" />
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
153        <receiver
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
154            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
156            android:enabled="false"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
157            android:exported="false" >
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
158            <intent-filter>
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
159                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
160                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
169                <action android:name="android.intent.action.BATTERY_OKAY" />
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
170                <action android:name="android.intent.action.BATTERY_LOW" />
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
179                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
180                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
189                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
193            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
198                <action android:name="android.intent.action.BOOT_COMPLETED" />
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
199                <action android:name="android.intent.action.TIME_SET" />
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
200                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
201            </intent-filter>
202        </receiver>
203        <receiver
203-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
204            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
205            android:directBootAware="false"
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
206            android:enabled="@bool/enable_system_alarm_service_default"
206-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
207            android:exported="false" >
207-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
208            <intent-filter>
208-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
209                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
213            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
213-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
215            android:enabled="true"
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
216            android:exported="true"
216-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
217            android:permission="android.permission.DUMP" >
217-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
218            <intent-filter>
218-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
219                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
220            </intent-filter>
221        </receiver>
222
223        <service
223-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
224            android:name="androidx.core.widget.RemoteViewsCompatService"
224-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
225            android:permission="android.permission.BIND_REMOTEVIEWS" />
225-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
226        <service
226-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
227            android:name="androidx.room.MultiInstanceInvalidationService"
227-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
228            android:directBootAware="true"
228-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
229            android:exported="false" />
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
230
231        <receiver
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
232            android:name="androidx.profileinstaller.ProfileInstallReceiver"
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
233            android:directBootAware="false"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
234            android:enabled="true"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
235            android:exported="true"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
236            android:permission="android.permission.DUMP" >
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
238                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
241                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
244                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
247                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
248            </intent-filter>
249        </receiver>
250    </application>
251
252</manifest>
