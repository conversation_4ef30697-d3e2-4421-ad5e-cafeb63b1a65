1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.despicable.chromeos.debug"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.WAKE_LOCK" />
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
11-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
12-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
13-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
14-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
15
16    <permission
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
17        android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="io.despicable.chromeos.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:5:5-51:19
23        android:name="io.despicable.chromeos.WeatherApplication"
23-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:6:9-43
24        android:allowBackup="true"
24-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:7:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee982a82fb86e03ba63249f20db390ab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:8:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:9:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:10:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:11:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:12:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:13:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.ChromeOS" >
35-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:14:9-46
36        <activity
36-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:16:9-26:20
37            android:name="io.despicable.chromeos.MainActivity"
37-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:17:13-41
38            android:exported="true"
38-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:18:13-36
39            android:label="@string/app_name"
39-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:19:13-45
40            android:theme="@style/Theme.ChromeOS" >
40-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:20:13-50
41            <intent-filter>
41-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:21:13-25:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:17-69
42-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:22:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:17-77
44-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:24:27-74
45            </intent-filter>
46        </activity>
47
48        <!-- Widget Configuration Activity -->
49        <activity
49-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:29:9-36:20
50            android:name="io.despicable.chromeos.presentation.ui.config.WidgetConfigActivity"
50-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:30:13-72
51            android:exported="false"
51-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:31:13-37
52            android:theme="@style/Theme.ChromeOS" >
52-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:32:13-50
53            <intent-filter>
53-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:33:13-35:29
54                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
54-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:17-87
54-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:34:25-84
55            </intent-filter>
56        </activity>
57
58        <!-- Weather Widget Provider -->
59        <receiver
59-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:39:9-50:20
60            android:name="io.despicable.chromeos.presentation.ui.widget.WeatherWidgetProvider"
60-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:40:13-73
61            android:exported="true" >
61-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:41:13-36
62            <intent-filter>
62-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:42:13-46:29
63                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
63-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:17-84
63-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:43:25-81
64                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:17-85
64-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:44:25-82
65                <action android:name="io.despicable.chromeos.ACTION_UPDATE_WEATHER" />
65-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:17-87
65-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:45:25-84
66            </intent-filter>
67
68            <meta-data
68-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:47:13-49:63
69                android:name="android.appwidget.provider"
69-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:48:17-58
70                android:resource="@xml/weather_widget_info" />
70-->C:\Users\<USER>\STUDIO\chromeOS_2\app\src\main\AndroidManifest.xml:49:17-60
71        </receiver>
72
73        <activity
73-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
74            android:name="androidx.glance.appwidget.action.ActionTrampolineActivity"
74-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
75            android:enabled="true"
75-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
76            android:excludeFromRecents="true"
76-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
77            android:exported="false"
77-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
78            android:theme="@android:style/Theme.NoDisplay" />
78-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
79        <activity
79-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
80            android:name="androidx.glance.appwidget.action.InvisibleActionTrampolineActivity"
80-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
81            android:enabled="true"
81-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
82            android:exported="false"
82-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
83            android:launchMode="singleInstance"
83-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
84            android:noHistory="true"
84-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
85            android:taskAffinity="androidx.glance.appwidget.ListAdapterCallbackTrampoline"
85-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
86            android:theme="@style/Widget.Glance.AppWidget.CallbackTrampoline" />
86-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
87
88        <receiver
88-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
89            android:name="androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver"
89-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
90            android:enabled="true"
90-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
91            android:exported="false" />
91-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
92        <receiver
92-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
93            android:name="androidx.glance.appwidget.UnmanagedSessionReceiver"
93-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
94            android:enabled="true"
94-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
95            android:exported="false" />
95-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
96        <receiver
96-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
97            android:name="androidx.glance.appwidget.MyPackageReplacedReceiver"
97-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
98            android:enabled="true"
98-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
99            android:exported="false" >
99-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
100            <intent-filter>
100-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
101                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
101-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
101-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
102            </intent-filter>
103        </receiver>
104
105        <service
105-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
106            android:name="androidx.glance.appwidget.GlanceRemoteViewsService"
106-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
107            android:exported="true"
107-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
108            android:permission="android.permission.BIND_REMOTEVIEWS" />
108-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1e3221b145c9b74bd642a03e5d8a29\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
109
110        <activity
110-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
111            android:name="androidx.compose.ui.tooling.PreviewActivity"
111-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
112            android:exported="true" />
112-->[androidx.compose.ui:ui-tooling-android:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7155561d762200be089d4877f3cdd6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
113
114        <provider
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
116            android:authorities="io.despicable.chromeos.debug.androidx-startup"
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
117            android:exported="false" >
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
118            <meta-data
118-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
119                android:name="androidx.work.WorkManagerInitializer"
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
120                android:value="androidx.startup" />
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
121            <meta-data
121-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.emoji2.text.EmojiCompatInitializer"
122-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
123                android:value="androidx.startup" />
123-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81df52121eca3e8b1fdb816add69cc44\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
125-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
126                android:value="androidx.startup" />
126-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94309e8c065092535228822469321bd\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
129                android:value="androidx.startup" />
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
130        </provider>
131
132        <service
132-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
133            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
133-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
134            android:directBootAware="false"
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
135            android:enabled="@bool/enable_system_alarm_service_default"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
136            android:exported="false" />
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
137        <service
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
138            android:name="androidx.work.impl.background.systemjob.SystemJobService"
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
140            android:enabled="@bool/enable_system_job_service_default"
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
141            android:exported="true"
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
142            android:permission="android.permission.BIND_JOB_SERVICE" />
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
143        <service
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
144            android:name="androidx.work.impl.foreground.SystemForegroundService"
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
146            android:enabled="@bool/enable_system_foreground_service_default"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
147            android:exported="false" />
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
148
149        <receiver
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
150            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
152            android:enabled="true"
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
154        <receiver
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
157            android:enabled="false"
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
158            android:exported="false" >
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
159            <intent-filter>
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
160                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
161                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
162            </intent-filter>
163        </receiver>
164        <receiver
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
165            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
167            android:enabled="false"
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
168            android:exported="false" >
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
169            <intent-filter>
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
170                <action android:name="android.intent.action.BATTERY_OKAY" />
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
171                <action android:name="android.intent.action.BATTERY_LOW" />
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
177            android:enabled="false"
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
178            android:exported="false" >
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
179            <intent-filter>
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
180                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
181                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
182            </intent-filter>
183        </receiver>
184        <receiver
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
186            android:directBootAware="false"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
187            android:enabled="false"
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
188            android:exported="false" >
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
189            <intent-filter>
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
190                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
194            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
196            android:enabled="false"
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
198            <intent-filter>
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
199                <action android:name="android.intent.action.BOOT_COMPLETED" />
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
200                <action android:name="android.intent.action.TIME_SET" />
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
201                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
202            </intent-filter>
203        </receiver>
204        <receiver
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
205            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
206            android:directBootAware="false"
206-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
207            android:enabled="@bool/enable_system_alarm_service_default"
207-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
208            android:exported="false" >
208-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
209            <intent-filter>
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
210                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
214            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
216            android:enabled="true"
216-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
217            android:exported="true"
217-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
218            android:permission="android.permission.DUMP" >
218-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
219            <intent-filter>
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
220                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
220-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
220-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc9789ec35507ddf2bbe74896c3192d4\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
221            </intent-filter>
222        </receiver>
223
224        <service
224-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
225            android:name="androidx.core.widget.RemoteViewsCompatService"
225-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
226            android:permission="android.permission.BIND_REMOTEVIEWS" />
226-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfcd53d77286ea764bdeb10a5454854d\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
227        <service
227-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
228            android:name="androidx.room.MultiInstanceInvalidationService"
228-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
229            android:directBootAware="true"
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
230            android:exported="false" />
230-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb70789ac6903ee3290ac157605813fc\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
231
232        <receiver
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
233            android:name="androidx.profileinstaller.ProfileInstallReceiver"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
234            android:directBootAware="false"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
235            android:enabled="true"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
236            android:exported="true"
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
237            android:permission="android.permission.DUMP" >
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
239                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
240            </intent-filter>
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
242                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
243            </intent-filter>
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
245                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
248                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16fc9ca23651e67ebb4d2fbb560b7cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
249            </intent-filter>
250        </receiver>
251    </application>
252
253</manifest>
