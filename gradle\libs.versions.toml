[versions]
agp = "8.10.0"
kotlin = "2.1.20"
coreKtx = "1.16.0"
lifecycleRuntimeKtx = "2.9.0"
activityCompose = "1.10.1"

# KotlinX
kotlinxCoroutines = "1.10.2"
kotlinxSerializationJson = "1.8.1"
kotlinx_collections_immutable = "0.3.8"
kotlinx_datetime = "0.6.2"

# AndroidX
datastore = "1.1.6"
glance = "1.1.1"
workmanager = "2.10.1"

# Compose
composeBom = "2025.05.00"
compose_navigation = "2.8.0-alpha13"
androidxNavigation = "2.9.0"
androidxHiltNavigationCompose = "1.2.0"
compose_adaptive = "1.1.0-beta01"

# License
aboutLibrary = "12.0.1"

# Koin
koin = "4.0.4"

reorderable = "2.4.3"

coil = "2.7.0"
javaxInject = "1"

room = "2.7.1"
ksp = "2.1.20-2.0.0"


[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
#appcompat = { module = "androidx.appcompat:appcompat", version = "1.7.0" }
#androidx-compose-foundation = { module = "androidx.compose.foundation:foundation" }
#androidx-compose-foundation-layout = { module = "androidx.compose.foundation:foundation-layout" }


# KotlinX
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutines" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
kotlinx_collections_immutable = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinx_collections_immutable" }
kotlinx_datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinx_datetime" }

# AndroidX
androidx_datastore = { module = "androidx.datastore:datastore-preferences-core", version.ref = "datastore" }
androidx_glance = { module = "androidx.glance:glance-appwidget", version.ref = "glance" }
androidx_glance_material3 = { module = "androidx.glance:glance-material3", version.ref = "glance" }
androidx_workmanager = { module = "androidx.work:work-runtime-ktx", version.ref = "workmanager" }

compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
compose_ui = { module = "androidx.compose.ui:ui" }
compose_icons = { module = "androidx.compose.material:material-icons-extended" }
compose_material3 = { module = "androidx.compose.material3:material3" }
compose_adaptive_navigation = { module = "org.jetbrains.compose.material3.adaptive:adaptive-navigation", version.ref = "compose_adaptive" }

compose_navigation = { module = "org.jetbrains.androidx.navigation:navigation-compose", version.ref = "compose_navigation" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "androidxHiltNavigationCompose" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" }


#Koin
koin_core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin_android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
koin_test = { module = "io.insert-koin:koin-test", version.ref = "koin" }
koin_compose = { module = "io.insert-koin:koin-compose-viewmodel", version.ref = "koin" }

# License
aboutlibrary-core = { group = "com.mikepenz", name = "aboutlibraries-core", version.ref = "aboutLibrary" }
aboutlibrary-compose = { group = "com.mikepenz", name = "aboutlibraries-compose", version.ref = "aboutLibrary" }


reorderable = { module = "sh.calvin.reorderable:reorderable", version.ref = "reorderable" }


javax-inject = { group = "javax.inject", name = "javax.inject", version.ref = "javaxInject" }
coil-kt = { group = "io.coil-kt", name = "coil", version.ref = "coil" }
coil-kt-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }


# room
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }


[bundles]
compose = ["compose.ui", "compose.icons", "compose.material3"]

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }


kotlin_serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
about-library = { id = "com.mikepenz.aboutlibraries.plugin", version.ref = "aboutLibrary" }
room = { id = "androidx.room", version.ref = "room" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }


