<variant
    name="debug"
    useSupportLibraryVectorDrawables="true"
    package="io.despicable.chromeos"
    minSdkVersion="30"
    targetSdkVersion="36"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.10.0"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d8cb1118758ae80152a27119404ff32\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\generated\ksp\debug\resources;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\processDebugResources\R.jar"
      type="MAIN"
      applicationId="io.despicable.chromeos.debug"
      generatedSourceFolders="build\generated\ksp\debug\java;build\generated\ksp\debug\kotlin;build\generated\ap_generated_sources\debug\out"
      generatedResourceFolders="build\generated\aboutLibraries\debug\res;build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d8cb1118758ae80152a27119404ff32\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
