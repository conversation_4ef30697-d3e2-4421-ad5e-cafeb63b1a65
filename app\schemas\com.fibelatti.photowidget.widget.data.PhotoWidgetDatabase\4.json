{"formatVersion": 1, "database": {"version": 4, "identityHash": "7f562f9e2e2d9396143ee6950fced313", "entities": [{"tableName": "local_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, `croppedPhotoPath` TEXT, `originalPhotoPath` TEXT, `externalUri` TEXT, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "croppedPhotoPath", "columnName": "croppedPhotoPath", "affinity": "TEXT", "notNull": false}, {"fieldPath": "originalPhotoPath", "columnName": "originalPhotoPath", "affinity": "TEXT", "notNull": false}, {"fieldPath": "externalUri", "columnName": "externalUri", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}, {"tableName": "displayed_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}, {"tableName": "photo_widget_order", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoIndex` INTEGER NOT NULL, `photoId` TEXT NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoIndex", "columnName": "photoIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}, {"tableName": "pending_deletion_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, `deletionTimestamp` INTEGER NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deletionTimestamp", "columnName": "deletionTimestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}, {"tableName": "excluded_widget_photos", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoId` TEXT NOT NULL, PRIMARY KEY(`widgetId`, `photoId`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoId"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '7f562f9e2e2d9396143ee6950fced313')"]}}