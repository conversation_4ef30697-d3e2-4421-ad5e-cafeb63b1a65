{"formatVersion": 1, "database": {"version": 1, "identityHash": "bf626604ad6149299ba3a484f665b707", "entities": [{"tableName": "photo_widget_order", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`widgetId` INTEGER NOT NULL, `photoIndex` INTEGER NOT NULL, `photoId` TEXT NOT NULL, PRIMARY KEY(`widgetId`, `photoIndex`))", "fields": [{"fieldPath": "widgetId", "columnName": "widgetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoIndex", "columnName": "photoIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "photoId", "columnName": "photoId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["widgetId", "photoIndex"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'bf626604ad6149299ba3a484f665b707')"]}}