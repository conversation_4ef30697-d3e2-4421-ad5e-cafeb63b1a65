package io.despicable.chromeos.presentation.ui.config

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import io.despicable.chromeos.domain.model.FontSize
import io.despicable.chromeos.domain.model.IconSize
import io.despicable.chromeos.presentation.viewmodel.WidgetConfigUiState
import io.despicable.chromeos.presentation.viewmodel.WidgetConfigViewModel

/**
 * Widget configuration screen with real-time preview
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WidgetConfigScreen(
    viewModel: WidgetConfigViewModel,
    onSaveClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    val TAG = "WidgetConfigScreen"

    val uiState by viewModel.uiState.collectAsState()
    val availableCities by viewModel.availableCities.collectAsState()
    val previewWeatherData by viewModel.previewWeatherData.collectAsState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Title
        Text(
            text = "Configure Weather Widget",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // Preview Section
            item {
                val currentConfig = remember(uiState) {
                    viewModel.getCurrentConfiguration()
                }
                PreviewSection(
                    weatherData = previewWeatherData,
                    configuration = currentConfig
                )
            }

            // City Selection
            item {
                CitySelectionSection(
                    cities = availableCities,
                    selectedCity = uiState.selectedCity,
                    onCitySelected = viewModel::onCitySelected
                )
            }

            // Icon Size Selection
            item {
                val iconSliderValue = remember(uiState.selectedIconSize) {
                    viewModel.getIconSizeSliderValue()
                }
                IconSizeSliderSection(
                    selectedIconSize = uiState.selectedIconSize,
                    sliderValue = iconSliderValue,
                    onIconSizeChanged = viewModel::onIconSizeSliderChanged
                )
            }

            // Font Size Selection
            item {
                val fontSliderValue = remember(uiState.selectedFontSize) {
                    viewModel.getFontSizeSliderValue()
                }
                FontSizeSliderSection(
                    selectedFontSize = uiState.selectedFontSize,
                    sliderValue = fontSliderValue,
                    onFontSizeChanged = viewModel::onFontSizeSliderChanged
                )
            }

            // Additional Options
            item {
                AdditionalOptionsSection(
                    showHumidity = uiState.showHumidity,
                    showWindSpeed = uiState.showWindSpeed,
                    onShowHumidityToggled = viewModel::onShowHumidityToggled,
                    onShowWindSpeedToggled = viewModel::onShowWindSpeedToggled
                )
            }

            // Save Button
            item {
                Button(
                    onClick = {
                        viewModel.saveConfiguration()
                        onSaveClicked()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    enabled = !uiState.isLoading
                ) {
                    if (uiState.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text("Save Widget Configuration")
                }
            }
        }
    }
}

@Composable
private fun PreviewSection(
    weatherData: io.despicable.chromeos.domain.model.WeatherData?,
    configuration: io.despicable.chromeos.domain.model.WidgetConfiguration
) {
    Column {
        Text(
            text = "Preview",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 12.dp)
        )

        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            WidgetPreviewComponent(
                weatherData = weatherData,
                configuration = configuration
            )
        }
    }
}

@Composable
private fun CitySelectionSection(
    cities: List<String>,
    selectedCity: String,
    onCitySelected: (String) -> Unit
) {
    Column {
        Text(
            text = "Select City",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(cities) { city ->
                FilterChip(
                    onClick = { onCitySelected(city) },
                    label = { Text(city) },
                    selected = city == selectedCity
                )
            }
        }
    }
}

@Composable
private fun IconSizeSliderSection(
    selectedIconSize: IconSize,
    sliderValue: Float,
    onIconSizeChanged: (Float) -> Unit
) {
    Column {
        Text(
            text = "Icon Size",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "${selectedIconSize.displayName} (${selectedIconSize.sizeDp}dp)",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Slider(
            value = sliderValue,
            onValueChange = onIconSizeChanged,
            valueRange = 0f..3f,
            steps = 2,
            modifier = Modifier.fillMaxWidth()
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "Small",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "Extra Large",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun FontSizeSliderSection(
    selectedFontSize: FontSize,
    sliderValue: Float,
    onFontSizeChanged: (Float) -> Unit
) {
    Column {
        Text(
            text = "Font Size",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "${selectedFontSize.displayName} (${selectedFontSize.sizeSp}sp)",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Slider(
            value = sliderValue,
            onValueChange = onFontSizeChanged,
            valueRange = 0f..3f,
            steps = 2,
            modifier = Modifier.fillMaxWidth()
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "Small",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "Extra Large",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun AdditionalOptionsSection(
    showHumidity: Boolean,
    showWindSpeed: Boolean,
    onShowHumidityToggled: (Boolean) -> Unit,
    onShowWindSpeedToggled: (Boolean) -> Unit
) {
    Column {
        Text(
            text = "Additional Information",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Switch(
                checked = showHumidity,
                onCheckedChange = onShowHumidityToggled
            )
            Text(
                text = "Show Humidity",
                modifier = Modifier.padding(start = 12.dp)
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Switch(
                checked = showWindSpeed,
                onCheckedChange = onShowWindSpeedToggled
            )
            Text(
                text = "Show Wind Speed",
                modifier = Modifier.padding(start = 12.dp)
            )
        }
    }
}